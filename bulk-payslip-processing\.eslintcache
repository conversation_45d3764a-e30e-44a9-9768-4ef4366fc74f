[{"/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/bindPayslipData.js": "1", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/checkPayslipExistence.js": "2", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/createPdf.js": "3", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/encryptPdf.js": "4", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/getConnection.js": "5", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/initiatePdfGeneration.js": "6", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/payslipFormation.js": "7", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/sendMail.js": "8", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/updateStatus.js": "9", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/employeetimezone.js": "10", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/prettier.config.js": "11", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/s3CommonFunction.js": "12", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/updateJobStatus.js": "13", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/updateStatusAndUploadFile.js": "14"}, {"size": 28448, "mtime": *************, "results": "15", "hashOfConfig": "16"}, {"size": 24127, "mtime": *************, "results": "17", "hashOfConfig": "16"}, {"size": 21063, "mtime": *************, "results": "18", "hashOfConfig": "16"}, {"size": 13247, "mtime": *************, "results": "19", "hashOfConfig": "16"}, {"size": 1267, "mtime": *************, "results": "20", "hashOfConfig": "16"}, {"size": 10063, "mtime": *************, "results": "21", "hashOfConfig": "16"}, {"size": 10299, "mtime": *************, "results": "22", "hashOfConfig": "16"}, {"size": 15081, "mtime": *************, "results": "23", "hashOfConfig": "16"}, {"size": 10137, "mtime": *************, "results": "24", "hashOfConfig": "16"}, {"size": 1981, "mtime": *************, "results": "25", "hashOfConfig": "16"}, {"size": 86, "mtime": *************, "results": "26", "hashOfConfig": "16"}, {"size": 2202, "mtime": *************, "results": "27", "hashOfConfig": "16"}, {"size": 2404, "mtime": 1586610103997, "results": "28", "hashOfConfig": "16"}, {"size": 1904, "mtime": 1586596185466, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "32"}, "1ew3m06", {"filePath": "33", "messages": "34", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "35"}, {"filePath": "36", "messages": "37", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "38"}, {"filePath": "39", "messages": "40", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "41"}, {"filePath": "42", "messages": "43", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "44"}, {"filePath": "45", "messages": "46", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "47"}, {"filePath": "48", "messages": "49", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "50"}, {"filePath": "51", "messages": "52", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "53"}, {"filePath": "54", "messages": "55", "errorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "56"}, {"filePath": "57", "messages": "58", "errorCount": 7, "warningCount": 0, "fixableErrorCount": 4, "fixableWarningCount": 0, "source": "59"}, {"filePath": "60", "messages": "61", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "errorCount": 15, "warningCount": 0, "fixableErrorCount": 6, "fixableWarningCount": 0, "source": "64"}, {"filePath": "65", "messages": "66", "errorCount": 8, "warningCount": 0, "fixableErrorCount": 4, "fixableWarningCount": 0, "source": "67"}, {"filePath": "68", "messages": "69", "errorCount": 9, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": "70"}, "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/bindPayslipData.js", ["71"], "// require jsdom\nconst {JSD<PERSON>} = require('jsdom');\n\n// Function to bind the values in payslip template\nmodule.exports = { \n    formpayslip: async(fileData,result,payslipType,employeeName,reportCreator,displayAddress) => {\n    try{\n        let myJSDom;\n        // adding footer for payslip\n        let fileData=fileData+'<div id=\"viewFormMonthlyPayslip\" style=\"text-align:center; font-size:10px; color:#000; margin-top:10px\">.\"This is system generated report. No signature is required\".</div>'\n        myJSDom = new JSDOM (fileData);\n        let $ = require('jquery')(myJSDom.window)\n\n        $('#formActionPayslipSubGrid').hide();\n\n        // get currency based on payslip type\n        let currency = payslipType == 'Monthly' ? result.Currency : result.Payslip['Currency'] ;\n        \n        // If Display_Payslip_Address is enabled need to show this payslip\n        if (displayAddress == 1) {\n            $(\"#payslipStreet1\").text(result.Payslip['Street1']+',');\n            $(\"#payslipStreet2\").text(result.Payslip['Street2']+',');\n            $(\"#payslipCity\").text(result.Payslip['City']+',');\n            $(\"#payslipState\").text(result.Payslip['State']+',');\n            $(\"#payslipCountry\").text(result.Payslip['Country']+',');\n            $(\"#payslipPinCode\").text(result.Payslip['Pincode']);\n        }\n        else {\n            $('#payslipTemplateAddress').html('');\n            $(\"#payslipStreet1\").text('');\n            $(\"#payslipStreet2\").text('');\n            $(\"#payslipCity\").text('');\n            $(\"#payslipState\").text('');\n            $(\"#payslipCountry\").text('');\n            $(\"#payslipPinCode\").text('');\n        }\n    \n        $(\"#formHeader\").text(result.Org_Name['Org_Name']);\n    \n        // Based on show report creator flag in org_details table\n        if (reportCreator) {\n            $('#viewFormMonthlyPayslip').append(`<div style=\"text-align:right; font-size:10px; color:#000; margin-top:10px\">Report generated by ${result.Payslip['First_Name']} ${result.Payslip['Last_Name']} on ${result.Payslip['Generated_On']} </div>`);\n        }\n    \n        $(\"#earnAmt\").html(``);\n        $(\"#deductAmt\").html(``);\n        $(\"#earnAmt\").text(\"Amount\" + ((currency) ? \"(\" + currency + \")\" : ''));\n        $(\"#deductAmt\").text(\"Amount\" + ((currency) ? \"(\" + currency + \")\" : ''));\n        $('#formActionPayslip').hide();\n        $('#formGeneratePayslip, #formGeneratePayslipSubGrid').hide();\n        $('#viewFormMonthlySalaryUser').show();\n        $('#viewFormMonthlyPayslip,#formReport').show();\n        \n        // Function to get the personal details\n        let personaldetails=fnUpdatePersonalDetailsInPayslip($,result,employeeName,payslipType)\n        console.log(\"personaldetails\",personaldetails);\n\n        // function to update earnings, deduction, organization contribution, and form16 tables\n        fngetTableContent($,$('#earningsTab'), $('#deductTab'), $('#exemptionsPanel'), $('#form16SummaryTab'), $('#sec10ExemptionsPanel'), $('#contributionTab1'), $('#contributionTab2'), result, false,payslipType);\n\n        let newhtml = myJSDom.serialize();\n        return newhtml;    \n    } \n    catch(error)\n    {\n       console.log('Error in binding values in template',error);\n        return '';\n    }\n}\n    \n}\n\n//Function to bind the personal details\nfunction fnUpdatePersonalDetailsInPayslip($,result,employeeName,payslipType){\n    // function to get leave details\n    let levDetails = fngetLeaveDetails($,result.PaidLeave[2],result.UnpaidLeave[2]);\n    let worked_count = payslipType == \"Monthly\" ? result.WorkedDays : result.actualHours['daysWorked'] ;\n    let paidleave = ((result.PaidLeave[1] != null) ? result.PaidLeave[1] : 0);\n    let pfPolicy = ((result.Payslip['Pf_PolicyNo'] != null) ? result.Payslip['Pf_PolicyNo'] : '-');\n    let unPaidleave = ((result.UnpaidLeave[1] != null) ? result.UnpaidLeave[1] : 0);\n\n    $(\"#payMonth\").text(\"Payslip for the Month of \" + result.Payslip_Month);\n    $(\"#viewMonthlyEmployeeId\").text(result.Payslip['User_Defined_EmpId']);\n    $(\"#viewMonthlyEmployeeName\").text(employeeName);\n    $(\"#viewMonthlyDesignation\").text(result.Payslip['Designation_Name']);\n    $(\"#viewMonthlyDateOfJoin\").text(result.Payslip['Date_Of_Join']);\n    $(\"#viewMonthlyDaysWorked\").text(worked_count);\n    $(\"#viewMonthlyPaidLeave\").text(paidleave + levDetails[0]);\n    $(\"#viewMonthlyUnpaidLeave\").text(unPaidleave + levDetails[1]);\n    $(\"#viewMonthlyDepartment\").text(result.Payslip['Department_Name']);\n    $(\"#viewMonthlyPFAccountNumber\").text(pfPolicy);\n    $(\"#viewMonthlyESI\").text((result.Payslip['Policy_No']) ? (result.Payslip['Policy_No']) : \"-\");\n    $(\"#viewMonthlyPANNo\").text(fnCheckNull(result.Emp_Pan));\n    $(\"#viewMonthlUAN\").text(fnCheckNull(result.Payslip['UAN']));\n    $(\"#viewMonthlyBankName\").text(fnCheckNull(result.Payslip['Bank_Name']));\n    $(\"#viewMonthlyBankAccountNo\").text(fnCheckNull(result.Payslip['Bank_Account_Number']));\n    $(\"#viewMonthlyDateOfRelieving\").text(fnCheckNull(result.Payslip['Emp_Relieving_Date']));\n    if(payslipType == \"Hourly\"){\n        $(\"#viewHoursWorked\").text(result.WorkedHours);\n        $(\"#viewDayWages\").text(result.actualHours['dayWage']);\n        $(\"#viewOTHours\").text(fnCheckNull(result.OverTimeHours));\n        $(\"#viewHoliday\").text(fnCheckNull(result.Holiday));\n    }\n}\n\n// function to get leave details\nfunction fngetLeaveDetails($,paidLev, unPaidLev){\n    let pLeave = paidLev;\n    let uPLeave = unPaidLev;\n    let leav, upleav,leavePaid,leaveUnpaid;\n    let lvarr = '';\n    let ulvarr = '';\n    $('#viewMonthlyPaidLeaveDetails').html('');\n    $('#viewMonthlyUnpaidLeaveDetails').html('');\n    let pLeaveLength=pLeave.length;\n        if (pLeaveLength > 0) {\n        leavePaid = '(';\n        for (let x = 0; x < pLeaveLength; x++) {\n            leav = pLeave[x][0].split(\" \");\n            lName = '';\n            if (leav.length > 1) {\n                for (let pl in leav) {\n                    lName += leav[pl].substring(0, 1);\n                }\n            }\n            else {\n                lName += pLeave[x][0].substring(0, 2);\n            }\n\n            lName = lName.toUpperCase();\n            leavePaid += lName + \" - \" + pLeave[x][1];\n            lvarr += lName + \" - \" + pLeave[x][0];\n\n            if (x != pLeave.length - 1) {\n                lvarr += \", \";\n                leavePaid += \", \";\n            }\n            $('#viewMonthlyPaidLeaveDetails').append('<tr class=\"child\"><td class=\"col-md-10 text-left\" id=\"pltype\" style=\"height:40px;word-break: break-word;\">' + pLeave[x][0] +'</td><td class=\"text-right\" id=\"plbalance\" style=\"height:40px\">' + pLeave[x][1] + '</td></tr>');\n        }\n        leavePaid += ')';\n    }\n    else {\n        leavePaid = '';\n        $('#viewMonthlyPaidLeaveDetails').append('<tr class=\"child\"><td class=\"col-md-10 text-left\" id=\"pltype\" style=\"height:40px;word-break: break-word;\">-</td><td class=\"text-center\" id=\"plbalance\" style=\"height:40px\">-</td></tr>');\n    }\n\n    let uPLeaveLength=uPLeave.length;\n    if (uPLeaveLength > 0) {\n        leaveUnpaid = '(';\n        for (let y = 0; y < uPLeaveLength; y++) {\n            upleav = uPLeave[y][0].split(\" \");\n            ulName = '';\n            if (upleav.length > 1) {\n                for (let ul in upleav) {\n                    ulName += upleav[ul].substring(0, 1);\n                }\n            }\n            else {\n                ulName += uPLeave[y][0].substring(0, 2);\n            }\n\n            ulName = ulName.toUpperCase();\n            leaveUnpaid += ulName + \" - \" + uPLeave[y][1];\n            ulvarr += (y === 0 && lvarr != '') ? (\", \" + ulName + \" - \" + uPLeave[y][0]) : (ulName + \" - \" + uPLeave[y][0]);\n\n            if (y != uPLeaveLength - 1) {\n                leaveUnpaid += \", \";\n                ulvarr += \", \";\n            }\n            $('#viewMonthlyUnpaidLeaveDetails').append('<tr class=\"child\"><td class=\"col-md-10 text-left\" id=\"upltype\" style=\"height:40px;word-break: break-word;\">' + uPLeave[y][0] +'</td><td class=\"text-right\" id=\"uplbalance\" style=\"height:40px\">' + uPLeave[y][1] + '</td></tr>');\n        }\n        leaveUnpaid += ')';\n    }\n    else {\n        leaveUnpaid = '';\n        $('#viewMonthlyUnpaidLeaveDetails').append('<tr class=\"child\"><td class=\"col-md-10 text-left\" id=\"upltype\" style=\"height:40px;word-break: break-word;\">-</td><td class=\"text-center\" id=\"uplbalance\" style=\"height:40px\">-</td></tr>');\n    }\n    let leaves = lvarr.concat(ulvarr);\n\n    if (leaves != '') {\n        $('#notes, #notesHourly').html('<b>Note :</b> ' + leaves);\n        $('#notes, #notesHourly').attr('style', 'display:\"\"');\n    }\n    else\n        $('#notes, #notesHourly').attr('style', 'display:none');\n\n    return [leavePaid,leaveUnpaid];\n}\n\n/* get table content */\nfunction fngetTableContent($,earnVar, deductVar, exempVar, form16Var, sec10Var, contribution1Var, contribution2Var, result, templateVariable,payslipType) {\n    function fngetOperation(val, var_type) {\n        if (!templateVariable) {\n            var_type.append(val)\n        }\n        else {\n            if (var_type == earnVar) {\n                earnVar += val;\n            }\n            else if (var_type == deductVar) {\n                deductVar += val;\n            }\n            else if (var_type == exempVar) {\n                exempVar += val;\n            }\n            else if (var_type == form16Var) {\n                form16Var += val;\n            }\n            else {\n                sec10Var += val;\n            }\n        }\n    }\n    let winSize;\n    let maxVal = Math.max((result.Deduction).length, (result.Incentive).length);\n    let totalIncentive = 0;\n    let totalDeduction = 0;\n    let height;\n    let emptyDed = 0;\n    let incent = 0;\n    let netPayAmt, currency, isPfEmployee, isIncuranceEmployee, isEtfEmployee;\n  \n    if (!templateVariable) {\n        earnVar.html('');\n        deductVar.html('');\n    }\n    for (let i = 0; i < maxVal; i++) {\n        if (result.Incentive[i]) {\n            if (result.Incentive[i]['Incentive_Name'] != undefined) {\n                incent++;\n                totalIncentive = parseFloat(totalIncentive) + parseFloat(result.Incentive[i]['Incentive_Amount']);\n\n                if (result.Incentive[i]['Description'] && (result.Incentive[i]['Incentive_Name'] != 'Provident Fund' && result.Incentive[i]['Incentive_Name'] != 'Insurance' && result.Incentive[i]['Incentive_Name'] != 'Advance Salary')) {\n                    $('#earnings').append();\n                    fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\" id=\"incent\" style=\"height:40px;word-break: break-word;\">' +/*result.Incentive[i]['Incentive_Name']+' - '+*/result.Incentive[i]['Description'] + '</td><td class=\"text-right\" id=\"incentAmt\" style=\"height:40px\">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);\n                }\n                else {\n                    if (result.Incentive[i]['Incentive_Name'] == 'Provident Fund') {\n                        fngetOperation('<tr id=\"incent\" class=\"child\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\"> Allowance - Other</td><td class=\"text-right\" style=\"height:40px\">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);\n                    }\n                    else if (result.Incentive[i]['Incentive_Name'] == 'Insurance') {\n                        fngetOperation('<tr id=\"incent\" class=\"child\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">Allowance - Insurance</td><td class=\"text-right\" style=\"height:40px\">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);\n                    }\n                    else {\n                        fngetOperation('<tr id=\"incent\" class=\"child\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">' + result.Incentive[i]['Incentive_Name'] + '</td><td class=\"text-right\" style=\"height:40px\">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);\n                    }\n                }\n            }\n            else {\n                \n                if (winSize > 991)\n                    fngetOperation('<tr class=\"child\"><td style=\"height:40px\"></td><td style=\"height:' + height + '\"></td></tr>', earnVar);\n                else\n                    $('#earndiv').attr({ 'style': 'display:none' });\n            }\n        }\n        else {\n            if (winSize < 991)\n                $('#earndiv').attr({ 'style': 'display:none' });\n            else\n                fngetOperation('<tr class=\"child\"><td style=\"height:40px\"></td><td style=\"height:' + height + '\"></td></tr>', earnVar);\n\n        }\n    }\n\n    for (let i = 0; i < maxVal; i++) {\n        if (result.Deduction[i]) {\n            if (result.Deduction[i]['Deduction_Name'] != undefined) {\n                totalDeduction = parseFloat(totalDeduction) + parseFloat(result.Deduction[i]['Deduction_Amount']);\n\n                if (result.Deduction[i]['Description']) {\n                    fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">'/*+result.Deduction[i]['Deduction_Name']+' - '*/ + result.Deduction[i]['Description'] + '</td><td class=\"text-right\" style=\"height:40px\">' + result.Deduction[i]['Deduction_Amount'] + '</td></tr>', deductVar);\n                }\n                else {\n                    fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">' + result.Deduction[i]['Deduction_Name'] + '</td><td class=\"text-right\" style=\"height:40px\">' + result.Deduction[i]['Deduction_Amount'] + '</td></tr>', deductVar);\n                }\n            }\n            else {\n                emptyDed++;\n                fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\"style=\"height:40px;word-break: break-word;\"></td><td style=\"height:40px\"></td></tr>', deductVar);\n            }\n        }\n        else {\n            emptyDed++;\n            fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\"></td><td style=\"height:40px\"></td></tr>', deductVar);\n        }\n    }\n\n    if(payslipType == \"Monthly\"){\n        netPayAmt   =  result.Payslip['Incentive_Amount'] ;\n        currency    =  result.Currency ;\n        isPfEmployee = result.salaryDetails.Is_PfEmployee;\n        isIncuranceEmployee = result.salaryDetails.Is_InsuranceEmployee;\n        isEtfEmployee = result.salaryDetails.Is_ETFEmployee\n    }else{\n        netPayAmt   =  result.Payslip['Total_Salary'];\n        currency    =  result.Payslip['Currency'];\n        isPfEmployee = result.salaryDetails.Is_Pf;\n        isIncuranceEmployee = result.salaryDetails.Is_Insurance;\n        isEtfEmployee = 0;\n    }\n    $(\"#totEarn, #netPay, #intermediatePayment, .viewInsuranceDetails, .viewFHInsuranceDetails\").remove();\n\n    // total earnings\n    fngetOperation('<tr class=\"child\" style=\"font-weight:bold;border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">Total Earnings</td><td class=\"text-right\" style=\"height:40px\">' + totalIncentive.toFixed(2) + '</td></tr>', earnVar);\n\n    // total deductions\n    fngetOperation('<tr class=\"child\" id=\"totEarn\" style=\"font-weight:bold;border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">Total Deductions</td><td class=\"text-right\" style=\"height:40px\">' + totalDeduction.toFixed(2) + '</td></tr>',deductVar);\n\n    // intermediate payment is available, then add empty cell in deduction\n    if (result.IntermediatePaymentAmt && result.IntermediatePaymentAmt > 0){\n        fngetOperation('<tr class=\"child\" style=\"font-weight:bold;border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">Total Intermediate Payment</td><td class=\"text-right\" style=\"height:40px\">' + (result.IntermediatePaymentAmt).toFixed(2) + '</td></tr>', earnVar);\n        fngetOperation('<tr class=\"child\" id=\"netPay\" style=\"font-weight:bold;border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\"></td><td class=\"text-right\" style=\"height:40px\"></td></tr>',deductVar);\n    }\n\n    // outstanding amount\n    fngetOperation('<tr class=\"child\" style=\"font-weight:bold;border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">Outstanding Amount</td><td class=\"text-right\" style=\"height:40px\">' + result.Payslip['Outstanding_Amt'] + '</td></tr>', earnVar);\n\n    // net pay\n    fngetOperation('<tr class=\"child\" id=\"netPay\" style=\"font-weight:bold;border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">Netpay</td><td class=\"text-right\" style=\"height:40px\">' + netPayAmt + '</td></tr>',deductVar);\n\n    if(netPayAmt){\n        $('#netpayInWord').html(currency ? `<b>: ${currency}</b>`+'  '+ number_to_words(netPayAmt) + ' Only' : ': '+number_to_words(netPayAmt) + ' Only');\n        $('#netpayInWord').attr('style', 'display:inline-block;');\n    }\n\n    if (isPfEmployee == 1 || isIncuranceEmployee == 1 || isEtfEmployee == 1 || result.orgLwfAmount > 0 ||  templateVariable ) {\n        $('#empContribution').attr('style', 'display:block;');\n        let contributionDetails = fngetContributionDetails(result.orgPfAmt,result.orgPfAmtHeader,result.adminCharge,result.edliCharge,result.etfAmt,result.orgLwfAmount,\n            result.orgLWFAmtHeader,result.InsuranceDetails,result.FixedHealthInsurance);\n        $('#contributionTab1,#contributionTab2').html('');\n        let contributeLength=contributionDetails.length;\n        for(i=0; i< contributeLength; i++){\n            if(i%2 == 0){\n                if(templateVariable){\n                    contribution1Var +=  contributionDetails[i];\n                }\n                else{\n                    contribution1Var.append(contributionDetails[i]);\n                }\n            }else{\n                if(templateVariable){\n                    contribution2Var +=  contributionDetails[i];\n                }\n                else{\n                    contribution2Var.append(contributionDetails[i]);\n                }\n            }\n        }\n        if(contributeLength%2 != 0){\n            if(templateVariable){\n                contribution2Var +=  '<tr class=\"child hidden-xs hidden-sm\" style=\"border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\"></td><td class=\"text-right\" style=\"height:40px\"></td></tr>';\n            }\n            else{\n                contribution2Var.append('<tr class=\"child hidden-xs hidden-sm\" style=\"border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\"></td><td class=\"text-right\" style=\"height:40px\"></td></tr>');\n            }\n        }\n    }\n    else{\n        $('#emptyContributionDiv').attr('style', 'height: 0px;');\n        $('#empContribution').attr('style', 'display:none');\n    }\n\n/** Form16 Summary **/\nif (result.Form16Exists && result.Form16Exists == 1 && payslipType == \"Monthly\") {\n    if (!templateVariable) {\n        form16Var.html('');\n        sec10Var.html('');\n        exempVar.html('');\n    }\n    let currencySymbol = ((currency) ? \"(\" + currency + \")\" : '');\n    $(\"#exemptionsAmt\").html(``);\n    $(\"#sectionsAmt\").html(``);\n    $(\"#form16SummaryAmt\").html(``);\n    $(\"#exemptionsAmt\").text(\"Amount\" + currencySymbol);\n    $(\"#sectionsAmt\").text(\"Amount\" + currencySymbol);    \n    $(\"#form16SummaryAmt\").text(\"Amount\" + currencySymbol);\n\n    /** Perks/Other Income/Exemptions/Rebates **/\n    let form16TaxDetails = result.Form16['form16TaxDetails'];\n    let form16Length=form16TaxDetails.length;\n    if (form16Length > 0) {\n        for (let i = 0; i < form16Length; i++) {\n            if ((form16TaxDetails[i]['ExemptionName'] !== '')) {\n                fngetOperation('<tr class=\"child\" id=\"E' + form16TaxDetails[i]['ExemptionName'] + '\"><td class=\"col-md-10 text-left\" style=\"height:40px\">' + form16TaxDetails[i]['ExemptionName'] + '</td><td class=\"text-right\" style=\"height:40px\">' + form16TaxDetails[i]['ExemptionAmount'] + '</td></tr>', exempVar);\n            }\n            else {\n                fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\"style=\"height:40px\"></td><td style=\"height:40px\"></td></tr>', exempVar);\n            }\n        }\n    }\n    else {\n        fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\"style=\"height:40px\"></td><td style=\"height:40px\"></td></tr>', exempVar);\n    }\n\n    /** Sec 10 Exemptions **/\n\n    let sec10Exemption = result.Form16['Sec10Exemption'];\n    let sec10Length=sec10Exemption.length;\n    if (sec10Length > 0) {\n        for (let i = 0; i < sec10Length; i++) {\n            if ((sec10Exemption[i]['Investment_Category'] != undefined && sec10Exemption[i]['Investment_Category'] != '')) {\n                fngetOperation('<tr class=\"child\" id=\"S' + sec10Exemption[i]['Investment_Category'] + '\"><td class=\"col-md-10 text-left\" style=\"height:40px\">' + sec10Exemption[i]['Investment_Category'] + '</td><td class=\"text-right\" style=\"height:40px\">' + sec10Exemption[i]['ExemptAmt'] + '</td></tr>', sec10Var);\n            }\n            else {\n                fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\"style=\"height:40px\"></td><td style=\"height:40px\"></td></tr>', sec10Var);\n            }\n        }\n    }\n    else {\n        fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\"style=\"height:40px\"></td><td style=\"height:40px\"></td></tr>', sec10Var);\n    }\n\n    /** Form16 Summary **/\n\n    let form16Summary = result.Form16['form16Summary'];\n    let form16Length=form16Summary.length;\n    if (form16Length > 0) {\n        for (let fs = 0; fs < form16Length; fs++) {\n\n\n            if (form16Summary[fs]['SummaryName'] != '') {\n                fngetOperation('<tr class=\"child\" id=\"' + form16Summary[fs]['SummaryName'] + '\"><td class=\"col-md-10 text-left\" style=\"height:40px\">' + form16Summary[fs]['SummaryName'] + '</td><td class=\"text-right\" style=\"height:40px\">' + form16Summary[fs]['SummaryAmount'] + '</td></tr>', form16Var);\n            }\n            else {\n                fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\" style=\"height:40px\"></td><td class=\"text-right\" style=\"height:40px\"></td></tr>', form16Var);\n            }\n        }\n    }\n    else {\n        fngetOperation('<tr class=\"child\"><td class=\"col-md-10 text-left\" style=\"height:40px\"></td><td class=\"text-right\" style=\"height:40px\"></td></tr>', form16Var);\n    }\n}\nelse {\n    $('#empForm16Table').attr('style', 'display:none');\n    $('#emptyForm16Div').removeAttr('style');\n    $('#form16SummaryTab tbody').html('');\n}\nreturn [earnVar, deductVar, exempVar, form16Var, sec10Var, contribution1Var, contribution2Var];\n\n}\n\n//  function to convert number to words\nfunction number_to_words(amount){\n    if(amount != 0){\n     let words = new Array();\n     words[0] = '';\n     words[1] = 'One';\n     words[2] = 'Two';\n     words[3] = 'Three';\n     words[4] = 'Four';\n     words[5] = 'Five';\n     words[6] = 'Six';\n     words[7] = 'Seven';\n     words[8] = 'Eight';\n     words[9] = 'Nine';\n     words[10] = 'Ten';\n     words[11] = 'Eleven';\n     words[12] = 'Twelve';\n     words[13] = 'Thirteen';\n     words[14] = 'Fourteen';\n     words[15] = 'Fifteen';\n     words[16] = 'Sixteen';\n     words[17] = 'Seventeen';\n     words[18] = 'Eighteen';\n     words[19] = 'Nineteen';\n     words[20] = 'Twenty';\n     words[30] = 'Thirty';\n     words[40] = 'Forty';\n     words[50] = 'Fifty';\n     words[60] = 'Sixty';\n     words[70] = 'Seventy';\n     words[80] = 'Eighty';\n     words[90] = 'Ninety';\n     amount = amount.toString();\n     let atemp = amount.split(\".\");\n     let number = atemp[0].split(\",\").join(\"\");\n     let n_length = number.length;\n     let words_string = \"\";\n     if (n_length <= 9) {\n         let n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);\n         for(let i = n_length-1,j = 8; i >= 0; i--,j--)\n         {\n             n_array[j] = +number[i];\n         }\n         for (let i = 0, j = 1; i < 9; i++, j++) {\n             if (i == 0 || i == 2 || i == 4 || i == 7) {\n                 if (n_array[i] == 1) {\n                     n_array[j] = 10 + parseInt(n_array[j]);\n                     n_array[i] = 0;\n                 }\n             }\n         }\n \n         let value = \"\";\n         for (let i = 0; i < 9; i++) {\n             if (i == 0 || i == 2 || i == 4 || i == 7) {\n                 value = n_array[i] * 10;\n             } else {\n                 value = n_array[i];\n             }\n             if (value != 0) {\n                 words_string += words[value] + \" \";\n             }\n             if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {\n                 words_string += \"Crore \";\n             }\n             if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {\n                 words_string += \"Lakh \";\n             }\n             if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {\n                 words_string += \"Thousand \";\n             }\n             if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {\n                 words_string += \"Hundred and \";\n             } else if (i == 6 && value != 0) {\n                 words_string += \"Hundred \";\n             }\n         }\n         words_string = words_string.split(\" \").join(\" \");\n     }\n     return words_string;\n    }else{\n        return 'Zero';\n    }\n }\n \n/**\n *  Check if the value is null or empty set '-'\n*/\nfunction fnCheckNull (value) {\n    return (value == null || value == '') ? '-' : value;\n}\n\n// function to get contribution details\nfunction fngetContributionDetails(orgPfAmt,orgPfAmtHeader,adminCharge,edliCharge,etfAmt,orgLwfAmount,orgLWFAmtHeader,InsuranceDetails,FixedHealthInsurance){\n    let contributionDetails=[];\n    // check pf is avilable\n    if (orgPfAmt > 0) {\n        contributionDetails.push('<tr class=\"child\" style=\"border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">' + orgPfAmtHeader + '</td><td class=\"text-right\" style=\"height:40px\">' + orgPfAmt + '</td></tr>');\n        // check adminCharge is avilable\n        if (adminCharge > 0){\n            contributionDetails.push('<tr class=\"child\" style=\"border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">' + 'Admin Charge' + '</td><td class=\"text-right\" style=\"height:40px\">' + adminCharge + '</td></tr>');\n        }\n        // check edliCharge is avilable\n        if (edliCharge > 0){\n            contributionDetails.push('<tr class=\"child\" style=\"border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">' + 'EDLI Charge' + '</td><td class=\"text-right\" style=\"height:40px\">' + edliCharge + '</td></tr>');\n        }\n    }\n    // check etfAmt is avilable\n    if (etfAmt > 0){\n        contributionDetails.push('<tr class=\"child\" style=\"border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">Org ETF Amount</td><td class=\"text-right\" style=\"height:40px\">' + etfAmt + '</td></tr>');\n    }\n    // check orgLwfAmount is avilable\n    if (orgLwfAmount > 0){\n        contributionDetails.push('<tr class=\"child\" style=\"border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">' + orgLWFAmtHeader + '</td><td class=\"text-right\" style=\"height:40px\">' + orgLwfAmount + '</td></tr>');\n    }\n    // check InsuranceDetails is avilable\n    if (InsuranceDetails.length > 0) {\n        for (i = 0; i < InsuranceDetails.length; i++) {\n            contributionDetails.push('<tr class=\"child\" style=\"border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">' + InsuranceDetails[i].InsuranceName + '</td><td class=\"text-right\" style=\"height:40px\">' + InsuranceDetails[i].Amount + '</td></tr>');\n        }\n    }\n    // check FixedHealthInsurance is avilable\n    if(FixedHealthInsurance.length > 0){\n        for (i = 0; i < FixedHealthInsurance.length; i++) {\n            contributionDetails.push('<tr class=\"child viewFHInsuranceDetails\" style=\"border-top:1px solid\"><td class=\"col-md-10 text-left\" style=\"height:40px;word-break: break-word;\">' + FixedHealthInsurance[i].Insurance_Name + '</td><td class=\"text-right\" style=\"height:40px\">' + FixedHealthInsurance[i].Insurance_Amount + '</td></tr>');\n        }\n    }\n    return contributionDetails;\n}", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/checkPayslipExistence.js", ["72"], "'use strict'\n// require the file to get the employee time zone\nimport { empTimeZoneDetails } from './employeetimezone';\n// require file to update the job status\nimport { statusUpdate} from './updateJobStatus';\n// require file to make database connection\nimport { makeConnection} from './getConnection';\n\n// Function to check whether payslip is already generated or not. \n// And get the details for generating payslip\nmodule.exports.startFunction = async(event, context) => {\n    console.log(\"event in check payslip existence function\",event);\n    let inputData;\n    // Since this function will be invoked from enpoint as well as lambda. So it is handle like this\n    if(event){\n        if(event.input){\n            inputData = event.input;\n        }\n        else{\n            inputData = event;\n        }\n    }\n\n    console.log(\"inputData for check payslip existence function\",inputData);\n\n    // variable declaration\n    let payslipType,salaryMonth,payslipId,employeeId,employeeTimeZone,employeeName,requestId,orgCode,templateId,Encrypted,jobType,loggedEmployeeId;\n\n    try{\n        // Input validation\n        if((!inputData.requestId) || (!inputData.orgCode) || (!inputData.templateId))\n        {\n            const response = \n            {\n                string: \"ErrorState\",\n                input:{\"errorCode\":\"IVE0000\",\"requestId\":requestId,\"orgCode\":orgCode,\"templateId\":templateId},\n                message: \"Error in payslip existence function\"\n            };\n            return response;                \n        }\n        else{\n            requestId=inputData.requestId;\n            orgCode=inputData.orgCode;\n            templateId=inputData.templateId;\n\n            // get database connection\n            let organizationDb = await makeConnection.createconnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);\n            if(organizationDb){\n                console.log(\"response after making database connection\",organizationDb);\n                // Retrieve the job_type and addedBy from bulk processing\n                return(\n                    organizationDb(\"bulk_processing\")\n                    .select('Job_Type','Added_By')\n                    .where('Request_Id',requestId)\n                    .andWhere('Job_Status','Inprogress')\n                    .then(async(getrecord) =>\n                        {\n                            console.log(\"get the job type\",getrecord);\n                            // Check whether data exists or not\n                            if(getrecord.length>0)\n                            {\n                                jobType=getrecord[0].Job_Type;\n                                loggedEmployeeId=getrecord[0].Added_By;\n                                // Retrieve payslipId,payslipType from payslip_bulk_processing table\n                                return(\n                                    organizationDb(\"payslip_bulk_processing\")\n                                    .select('Payslip_Id','Payslip_Type')\n                                    .where('Request_Id',requestId)\n                                    .andWhere('Generation_Status','Open')\n                                    .then(async(version) =>\n                                        {\n                                            console.log(\"get payslip which need to processed\",version);\n                                            if(version.length>0){\n                                            payslipId=version[0].Payslip_Id;\n                                            payslipType=version[0].Payslip_Type;     \n                                            // formation of payslip table name based on type          \n                                            let payslipTable = (payslipType===\"Monthly\") ? \"salary_payslip\" : \"hourlywages_payslip\"\n\n                                            // Get the empId,S3_File_Encrypted and S3_filename from payslip table\n                                            return(\n                                            organizationDb(payslipTable)\n                                            .select('Employee_Id','S3_FileName','Salary_Month','S3_File_Encrypted')\n                                            .where('Payslip_Id',payslipId)\n                                            .then(async(payslipData) =>\n                                            {\n                                                // check payslipdata exist or not\n                                                if(payslipData.length>0){\n                                                console.log(\"retrieve payslip data\",payslipData);\n\n                                                let payslipFileName=payslipData[0].S3_FileName;\n                                                employeeId=payslipData[0].Employee_Id;\n                                                salaryMonth=payslipData[0].Salary_Month;\n                                                Encrypted=payslipData[0].S3_File_Encrypted;\n\n                                                // Check whether job type is email\n                                                if(jobType===\"Email\")\n                                                {    \n                                                    // get the emp mailId based on empId from emp_job table\n                                                    return(\n                                                        organizationDb(\"emp_job\")\n                                                        .select('Emp_Email')\n                                                        .where('Employee_Id',employeeId)\n                                                        .then(async(getemail) =>\n                                                        {\n                                                            console.log(\"getemail id\",getemail);\n                                                            if(getemail.length>0)\n                                                            {\n                                                                let emailId=getemail[0].Emp_Email;\n                                                                // get the employeename\n                                                                let getPersonalData= await getEmployeeName(organizationDb,employeeId);\n                                                                console.log(\"getPersonalData\",getPersonalData);\n                                                                // check whether errcode is returned or not\n                                                                if(getPersonalData.errCode===\"\")\n                                                                {\n                                                                    // if email and filename exist them move to mail step\n                                                                    if(emailId){\n                                                                        // check filename exist or not\n                                                                        if(payslipFileName)\n                                                                        {\n                                                                            // update the status in payslip bulk processing table.\n                                                                            let updateGenerationStatus= await updateRequestStatus(loggedEmployeeId,organizationDb,payslipId,requestId);\n                                                                            console.log(\"updateGenerationStatus\",updateGenerationStatus);\n                                                                            if(updateGenerationStatus===\"success\"){\n                                                                                const response = \n                                                                                {\n                                                                                    string:'Mail',\n                                                                                    input:{\"requestId\":requestId,\"payslipId\":payslipId,\"orgCode\":orgCode,\"emailId\":emailId,\"payslipFileName\":payslipFileName,\"templateId\":templateId,\"employeeName\":getPersonalData.employeeName,\"payslipMonth\":salaryMonth,\"payslipType\":payslipType,\"Encrypted\":Encrypted,\"addedBy\":loggedEmployeeId},\n                                                                                    message: \"Send email to that employee\"\n                                                                                };\n                                                                                return response;  \n                                                                            }\n                                                                            else{\n                                                                                throw updateGenerationStatus;\n                                                                            }\n                                                                        }\n                                                                        else{\n                                                                            console.log(\"EmailId exist for this employee,need to generate payslip\");\n                                                                            // Since file is not generated move to payslip formation\n                                                                            const response = \n                                                                            {\n                                                                                string:'NextStep',\n                                                                                input:{\"requestId\":requestId,\"payslipId\":payslipId,\"orgCode\":orgCode,\"templateId\":templateId,\"payslipType\":payslipType,\"employeeId\":employeeId,\"salaryMonth\":salaryMonth,\"employeeName\":getPersonalData.employeeName,\"addedBy\":loggedEmployeeId},\n                                                                                message: \"Move to payslip formation step\"\n                                                                            };\n                                                                            return response;\n                                                                        }\n                                                                    }\n                                                                    else{\n                                                                        console.log(\"Email not exist for this employee\")\n                                                                        throw \"PBP0106\"\n                                                                    }\n                                                                }\n                                                                else{\n                                                                    const response = \n                                                                    {\n                                                                        string: \"ErrorState\",\n                                                                        input:{\"errorCode\":getPersonalData.errCode,\"payslipId\":payslipId,\"requestId\":requestId,\"orgCode\":orgCode,\"templateId\":templateId,\"addedBy\":loggedEmployeeId},\n                                                                        message: \"Error in payslip existence function\"\n                                                                    };\n                                                                    return response;     \n                                                                }\n                                                            }\n                                                            else{\n                                                                console.log(\"Email not exist for this employee\");\n                                                                throw \"PBP0106\";\n                                                            }\n                                                        })\n                                                    )\n                                                }\n                                                else{\n                                                    // get the employeename\n                                                    let getPersonalData= await getEmployeeName(organizationDb,employeeId);\n                                                    console.log(\"getPersonalData\",getPersonalData);\n                                                    // Check whether errocode exist or not\n                                                    if(getPersonalData.errCode===\"\")\n                                                    {\n                                                        if(payslipFileName)\n                                                        {\n                                                            // update the status in payslip bulk processing table\n                                                            let updateGenerationStatus= await updateRequestStatus(loggedEmployeeId,organizationDb,payslipId,requestId)\n                                                            console.log(\"updateGenerationStatus\",updateGenerationStatus);\n                                                            if(updateGenerationStatus===\"success\"){\n                                                                //  if job type is generation\n                                                                const response = \n                                                                {\n                                                                    string: \"Step1\",\n                                                                    input:{\"requestId\":requestId,\"templateId\":templateId,\"orgCode\":orgCode},\n                                                                    message: \"Check for other payslip\"\n                                                                };\n                                                                return response;\n                                                            }\n                                                            else{\n                                                                throw updateGenerationStatus;\n                                                            }\n                                                        }\n                                                        else{\n                                                            console.log(\"Payslip pdf need to generate\")\n                                                            const response = \n                                                            {\n                                                                string:'NextStep',\n                                                                input:{\"requestId\":requestId,\"payslipId\":payslipId,\"orgCode\":orgCode,\"templateId\":templateId,\"payslipType\":payslipType,\"employeeId\":employeeId,\"salaryMonth\":salaryMonth,\"employeeName\":getPersonalData.employeeName,\"addedBy\":loggedEmployeeId},\n                                                                message: \"Move to payslip formation step\"\n                                                            };\n                                                            return response;\n                                                        }\n                                                    }\n                                                    else{\n                                                        const response = \n                                                        {\n                                                            string: \"ErrorState\",\n                                                            input:{\"errorCode\":getPersonalData.errCode,\"payslipId\":payslipId,\"requestId\":requestId,\"orgCode\":orgCode,\"templateId\":templateId,\"addedBy\":loggedEmployeeId},\n                                                            message: \"Error in payslip existence function\"\n                                                        };\n                                                        return response;   \n                                                    }\n\n                                                }\n                                                }\n                                                else{\n                                                    console.log(\"No data found in payslip table\");\n                                                    throw \"_EC0001\"\n                                                }\n                                            })\n                                    )\n                                        }\n                                        else{\n                                            console.log(\"No data found in payslip bulk processing table \")\n                                            // calculate the timestamp based on logged empId and update the status\n                                            employeeTimeZone = await empTimeZoneDetails.getEmployeeTimeZone(loggedEmployeeId, organizationDb)\n                                            // function to update the status in bulk processing table\n                                            let statusupdate= await statusUpdate.updateJobStatus(organizationDb,\"Completed\",employeeTimeZone,requestId,jobType)\n                                            if(statusupdate===\"completed\"){\n                                                console.log(\"Job process is completed\");\n                                            }\n                                            else\n                                            {\n                                                console.log(\"Job is Inprogress\");\n                                            }\n                                            const response = \n                                            {\n                                                string: \"Success\",\n                                                message: \"Step function completed\"\n                                            };\n                                            return response;\n                                        }\n                                    })\n                                )\n                            }\n                            else{\n                                console.log(\"No data exist in bulk_processing table for processing.\")\n                                const response = \n                                {\n                                    string: \"Success\",\n                                    message: \"Step function completed\"\n                                };\n                                return response;\n                            }\n                        })\n                        .catch((err) => \n                        {\n                            console.log(\"Error in check payslip existence catch block\",err);\n                            let errCode= (err===\"_EC0001\" || \"PBP0106\")? err : \"PBP0111\";\n                            const response = \n                            {\n                                string: \"ErrorState\",\n                                input:{\"errorCode\":errCode,\"payslipId\":payslipId,\"requestId\":requestId,\"orgCode\":orgCode,\"templateId\":templateId,\"addedBy\":loggedEmployeeId},\n                                message: \"Error in payslip existence function\"\n                            };\n                            return response;                \n                        })\n                /**return the success result to user */\n                .then(function (result) {\n                    return result;\n                })\n                /**catch organizationDbConnection connectivity errors */\n                .catch(function (err) {\n                    console.log('error in catch block of check payslip existence', err);\n                    const response = \n                    {\n                        string: \"ErrorState\",\n                        input:{\"errorCode\":\"PBP0111\",\"payslipId\":payslipId,\"requestId\":requestId,\"orgCode\":orgCode,\"templateId\":templateId,\"addedBy\":loggedEmployeeId},\n                        message: \"Error in payslip existence function\"\n                    };\n                    return response;                \n                })\n                // destroy the connection\n                .finally(() => {\n                    organizationDb.destroy();\n                })\n                )\n            }\n            else{\n                console.log(\"Error in making database connection\");\n                const response = \n                {\n                    string: \"ErrorState\",\n                    input:{\"errorCode\":\"_DB0000\",\"payslipId\":payslipId,\"requestId\":requestId,\"orgCode\":orgCode,\"templateId\":templateId,\"addedBy\":loggedEmployeeId},\n                    message: \"Error in payslip existence function\"\n                };\n                return response;      \n            }\n        }\n    }\n    catch(error){\n        console.log(\"Error in check payslip existence function()\",error);\n        const response = \n        {\n          string: \"ErrorState\",\n          input:{\"errorCode\":\"PBP0111\",\"payslipId\":payslipId,\"requestId\":requestId,\"orgCode\":orgCode,\"templateId\":templateId,\"addedBy\":loggedEmployeeId},\n          message: \"Error in payslip existence function\"\n        };\n        return response;                \n    }\n};\n\n// get the employee name from emp personal table\nasync function getEmployeeName(organizationDb,employeeId){\n    try{\n        return(\n            organizationDb.raw('select  CONCAT_WS (\" \", Emp_First_Name,Emp_Middle_Name,Emp_Last_Name) as name from emp_personal_info where Employee_Id= \"'+employeeId+'\"')\n            .then(async(personalData) =>\n            {\n                // check the length of data\n                if(personalData[0].length>0){\n                    console.log(\"get personalData\",personalData);\n                    employeeName=personalData[0][0].name;\n                    console.log(\"employeeName\",employeeName);\n                    return ({\"errCode\":\"\",\"employeeName\":employeeName})\n                }\n                else{\n                    console.log(\"No personal details found\");\n                    return ({\"errCode\":\"_EC0001\"})\n                }\n            })\n            .catch(function (err) {\n                console.log('error in catch block in retrieve personal data and email', err);\n                return ({\"errCode\":\"PBP0002\"})\n                \n            })\n        )\n    }\n    catch(error){\n        console.log(\"Error in getEmail function catch block\",error);\n        return ({\"errCode\":\"PBP0002\"})\n    }\n}\n\n// update the generation status in payslip bulk processing table\nasync function updateRequestStatus(loggedEmployeeId,organizationDb,payslipId,requestId){\n    try{\n        // calculate employee timezone\n        employeeTimeZone = await empTimeZoneDetails.getEmployeeTimeZone(loggedEmployeeId, organizationDb)\n        console.log(\"calculate timestamp\",employeeTimeZone);\n        // Update the generation_status as Already generated in payslip_bulk_processing table\n        return(\n            organizationDb(\"payslip_bulk_processing\")\n            .update({ \"Generation_Status\": 'Already Generated',\"Updated_On\":employeeTimeZone })\n            .where('Payslip_Id',payslipId)\n            .andWhere('Request_Id',requestId)\n            .then((updateStatus) =>\n            { \n                return \"success\";\n            })\n            .catch(function (err) {\n                console.log('error in catch block of updating status', err);\n                return \"PBP0001\";\n                \n            })\n        )\n    }\n    catch(err){\n        console.log(\"Error in updateGenerationStatus catch block\",err);\n        return \"PBP0001\";\n    }\n}\n", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/createPdf.js", ["73"], "'use strict'\nconst fs=require('fs');\nconst chromium = require('chrome-aws-lambda');\n// require file to delete/upload/retrieve file in  S3\nimport {crudS3Function} from './s3CommonFunction';\n// require the file to get the employee time zone\nimport {empTimeZoneDetails} from './employeetimezone';\n// require file to make database connection\nimport { makeConnection} from './getConnection';\n// require file to update status\nimport {changeStatus} from './updateStatusAndUploadFile';\n\n// Function to form payslip and convert to base64 data\nmodule.exports.pdfCreation =async (event,context) => {\n    console.log('event for create pdf function',event);\n    let inputData;\n\n    // Get the html content of payslip\n    let input=event.input;\n    let inputDataPath=input.draftFileNamePath;\n\n    let payslipId=input.payslipId;\n    let requestId=input.requestId;\n    let addedBy=input.addedBy;\n    let employeeId=input.employeeId;\n    let orgCode=input.orgCode;\n    let templateId=input.templateId;\n    let employeeName=input.employeeName;\n    let payslipMonth=input.payslipMonth;\n    let payslipType=input.payslipType;\n    try{\n        // get database connection\n        let organizationDb = await makeConnection.createconnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);\n        if(organizationDb){\n            console.log('response after making database connection',organizationDb);\n            // Retrieve the job_type from bulk processing\n            return(\n                organizationDb('bulk_processing')\n                .select('Job_Type')\n                .where('Request_Id',requestId)\n                .then(async(getrecord) =>\n                {\n                    console.log('get the job type',getrecord);\n                    // Check whether data exists or not\n                    if(getrecord.length>0)\n                    {\n                        let jobType=getrecord[0].Job_Type;\n                        // check Payslip_Password_Protected flag in org details table\n                        return(\n                            organizationDb('org_details')\n                            .select('Payslip_Password_Protected')\n                            .then(async(getOrgDetails) =>\n                            {\n                                console.log('get data from organization details table',getOrgDetails);\n                                // Check whether data exists or not\n                                if(getOrgDetails.length>0)\n                                {\n                                    let isEncrypted=getOrgDetails[0].Payslip_Password_Protected;\n                                    // Get the payslip template data from s3 bucket\n                                    let fileData= await crudS3Function.getS3file(inputDataPath,process.env.bucketName)\n                                    console.log('response after getting file from s3',fileData)\n                                    let content=fileData.Body;\n                                    // Convert buffer to string\n                                    inputData=content.toString('utf8');\n\n                                    // get payslip year\n                                    let payslipYear=payslipMonth.split(',')[1];\t\n\n                                    // example 04,2019 - convert as 042019\n                                    let definedPayslipMonth= payslipMonth.replace(/[,\\s]/g, '');\n\n                                    // Remove the draft file created in s3 bucket\n                                    let removefile= await crudS3Function.deleteFile(inputDataPath,process.env.bucketName)\n                                    console.log('delete the draft file',removefile)\n\n                                    // file will be created and deleted within with function\n                                    let pathOfFileToEncrypt= '/tmp/'+orgCode+requestId+'Payslip'+payslipId+'.pdf';\n                                    console.log('File in tmp folder',pathOfFileToEncrypt);\n\n                                    // pdf will be created based on the options\n                                    const pdfOptions = {\n                                        path: pathOfFileToEncrypt,\n                                        format: 'A4',\n                                        margin: {\n                                            top: 0,\n                                            right: 0,\n                                            bottom: 0,\n                                            left: 0\n                                        },\n                                        printBackground: true \n                                    };\n                                    \n                                    // create browser\n                                    const browser = await chromium.puppeteer.launch({\n                                        args: chromium.args,\n                                        defaultViewport: chromium.defaultViewport,\n                                        executablePath: await chromium.executablePath,\n                                        headless: true,\n                                    });\n                                \n                                \n                                    console.log('browser',browser)\n                                \n                                    const page = await browser.newPage();\n                                \n                                    // set the content in a pdf file\n                                    await page.setContent(inputData);\n                                \n                                    let content=await page.pdf(pdfOptions);        \n                                    console.log('pdf content',content);\n\n                                    // Convert the buffer to base64 data\n                                    let base64data=content.toString('base64');\n                                    console.log('pdf base64 data',base64data);\n\n                                    await page.close();\n\n                                    // Check whether file need to encrypt or not\n                                    if(!isEncrypted){\n                                    try{\n                                        return(\n                                            // Get user defined empId and emailid from emp_job table\n                                            organizationDb('emp_job')\n                                            .select('User_Defined_EmpId','Emp_Email')\n                                            .where('Employee_Id', employeeId)\n                                            .then(async(getUserDefinedId) =>\n                                            {\n                                                // Check whether data exists or not\n                                                if(getUserDefinedId.length>0)\n                                                {\n                                                    // user defined empId\n                                                    let userDefinedEmpId=getUserDefinedId[0].User_Defined_EmpId;\n                                                    let emailId=getUserDefinedId[0].Emp_Email;\n\n                                                    // Formation of payslipFileName example: Payslip_10_042019.pdf\n                                                    let payslipFileName=process.env.payslipFileNamePrefix+userDefinedEmpId+'_'+definedPayslipMonth+'.pdf';\n                                                    \n                                                    // formation of s3 bucket path\n                                                    let path=process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+payslipYear+'/'+ payslipType+'/'+payslipFileName;\n                                                    // upload the generated payslip pdf in s3 bucket\n                                                    let uploadFileData= await crudS3Function.uploadS3file(content,path,process.env.bucketName,'application/pdf')\n                                                    console.log('Response after uploading s3 file',uploadFileData)\n                                                    // if file is uploaded in s3 update the status\n                                                    if(uploadFileData)\n                                                    {\n                                                        // get employee timestamp based on location\n                                                        let employeeTimeZone = await empTimeZoneDetails.getEmployeeTimeZone(addedBy, organizationDb)\n                                                        console.log('employeeTimeZone',employeeTimeZone);\n                                                        // update status,file name and isEncrypted flag as 0 (Since in this fucntion encryption is not done) in database\n                                                        let updateRecord= await changeStatus.updateStatusAndS3Upload(organizationDb,payslipFileName,requestId,payslipId,payslipType,employeeTimeZone,0,employeeId)\n                                                        console.log('response after updating filename and status',updateRecord);\n                                                        if(updateRecord){\n                                                            // delete the file in tmp folder\n                                                            let removefile = await removeFile(pathOfFileToEncrypt)\n                                                            console.log('removefile',removefile);\n                                                            // If the requested job type is email move to send mail step\n                                                            if(jobType==='Email')\n                                                            {\n                                                                // Since encrypted is passed as 0 since pdf is not encrypted\n                                                                const response = \n                                                                {\n                                                                    string: 'Mail',\n                                                                    input:{'requestId':requestId,'payslipId':payslipId,'orgCode':orgCode,'addedBy':addedBy,'emailId':emailId,'payslipFileName':payslipFileName,'templateId':templateId,'employeeName':employeeName,'payslipMonth':payslipMonth,'payslipType':payslipType,'Encrypted':0},\n                                                                    message: 'Pdf generated successfully'\n                                                                };\n                                                                return response;\n                                                            }\n                                                            // Download - will be handled in future\n                                                            // if the job type is Generation then move to step1\n                                                            else{\n                                                                const response = \n                                                                {\n                                                                    string: 'Step1',\n                                                                    input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},\n                                                                    message: 'Pdf generated successfully'\n                                                                };\n                                                                return response; \n                                                            }\n                                                        }\n                                                        else{\n                                                            console.log('Error in updating status and filename in table');\n                                                            // delete the file in tmp folder\n                                                            let removefile = await removeFile(pathOfFileToEncrypt)\n                                                            console.log('removefile',removefile);\n                                                            let response= await outputResponse('PBP0001',payslipId,requestId,addedBy,orgCode,templateId);\n                                                            return response;\n                                                        }                                                 \n                                                    }\n                                                    else{\n                                                        console.log('Error in uploading pdf in s3 bucket');\n                                                        // delete the file in tmp folder\n                                                        let removefile = await removeFile(pathOfFileToEncrypt)\n                                                        console.log('removefile',removefile);\n                                                        let response= await outputResponse('PBP0104',payslipId,requestId,addedBy,orgCode,templateId);\n                                                        return response;\n                                                    }\n                                                }\n                                                else{\n                                                    console.log('User defined employeeid not exist for this employee');\n                                                    // delete the file in tmp folder\n                                                    let removefile = await removeFile(pathOfFileToEncrypt)\n                                                    console.log('removefile',removefile);\n                                                    let response= await outputResponse('_EC0001',payslipId,requestId,addedBy,orgCode,templateId);\n                                                    return response;\n                                                }\n                                            })\n                                            .catch(async function(err) {\n                                                console.log('Error in retrieve user defined employee id',err);\n                                                // delete the file in tmp folder\n                                                let removefile = await removeFile(pathOfFileToEncrypt)\n                                                console.log('removefile',removefile);\n                                                let response= await outputResponse('PBP0002',payslipId,requestId,addedBy,orgCode,templateId)\n                                                return response;\n                                            })\n                                        )\n                                    }\n                                    catch(error){\n                                        console.log('error in uploading file and status update catch block',error);\n                                        // delete the file in tmp folder\n                                        let removefile = await removeFile(pathOfFileToEncrypt)\n                                        console.log('removefile',removefile);\n                                        let response= await outputResponse('PBP0104',payslipId,requestId,addedBy,orgCode,templateId);\n                                        return response;                                      \n                                    }\n                                    }\n                                    else{\n                                        // delete the file in tmp folder\n                                        let removefile = await removeFile(pathOfFileToEncrypt)\n                                        console.log('removefile',removefile);\n\n                                        // upload the base64 data in s3 and pass the path to generatepdf function\n                                        let draftFileName='Draft_'+orgCode+requestId+payslipId+'base64.txt';\n                                        let path= process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+payslipYear+'/'+payslipType+'/'+draftFileName;\n                                        let fileData= await crudS3Function.uploadS3file(base64data,path,process.env.bucketName,'application/txt')\n                                        console.log('response after uploading draft file in s3',fileData)\n                                        if(fileData){\n                                            const response = \n                                            {\n                                                string: 'GeneratePdf',\n                                                input:{'payslipId':payslipId,'requestId':requestId,'employeeId':employeeId,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName,'draftFileName':path,'payslipMonth':payslipMonth,'payslipType':payslipType,'addedBy':addedBy},\n                                                message: 'Payslip formation completed successfully'\n                                            };\n                                            return response; \n                                        }\n                                        else{\n                                            console.log('Error in uploading file in s3')\n                                            let response= await outputResponse('PBP0104',payslipId,requestId,addedBy,orgCode,templateId);  \n                                            return response;                                    \n                                        }\n                                    }\n                                }\n                                else{\n                                    console.log('No records exist in organization details');\n                                    let response= await outputResponse('_EC0001',payslipId,requestId,addedBy,orgCode,templateId);  \n                                    return response;                                    \n                                }\n                            })\n                        )\n                    }\n                    else{\n                        console.log('No records found in bulk processing table');\n                        let response= await outputResponse('_EC0001',payslipId,requestId,addedBy,orgCode,templateId);\n                        return response;                                      \n                    }\n                })\n                .catch(async function(err) {\n                    console.log('Error in retrieve job type',err);\n                    let response= await outputResponse('PBP0002',payslipId,requestId,addedBy,orgCode,templateId);     \n                    return response;                                 \n                })\n                .finally(() => {\n                    organizationDb.destroy();\n            }))\n    }\n    else{\n        console.log('Error in making database connection');\n        let response= await outputResponse('_DB0000',payslipId,requestId,addedBy,orgCode,templateId);    \n        return response;                                  \n    } \n    }\n    catch(err){\n        console.log('Error in creating pdf catch block',err);\n        // Remove the draft file created in s3 bucket\n        let removefile= await crudS3Function.deleteFile(inputDataPath,process.env.bucketName)\n        console.log('remove draft file in s3 bucket',removefile)\n        let response= await outputResponse('PBP0108',payslipId,requestId,addedBy,orgCode,templateId);  \n        return response;                                    \n    }\n}\n\n// Function to remove file in temp folder\nasync function removeFile(pathOfFileToEncrypt){\n    console.log('path of FileToEncrypt',pathOfFileToEncrypt);\n    return new Promise(function (resolve, reject) {\n\n        // Function to delete the file created in temp folder\n       fs.unlink(pathOfFileToEncrypt, (err) => {\n            if (err) \n            {\n                console.log('Error in deleting file in tmp folder',err);\n                resolve('');\n            }\n            else{\n                console.log('file deleted successfully');\n                resolve('success');\n            }\n        });  \n    })\n}\n\n// return output response\nfunction outputResponse(errCode,payslipId,requestId,addedBy,orgCode,templateId){\n    const response = \n    {\n        string: 'ErrorState',\n        input:{'errorCode':errCode,'payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId},\n        message: 'Error occured in payslip creation function'\n    };\n    return response;\n}\n", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/encryptPdf.js", ["74"], "'use strict'\n//require fs package\nconst fs = require('fs');\n// require aws-sdk to use aws services\nconst AWS = require('aws-sdk');\n// Create object for s3 bucket\nconst S3 = new AWS.S3();\nconst { spawn } = require('child_process');\n//require file to get the timezone\nimport {empTimeZoneDetails} from './employeetimezone';\n//require file to make database connection\nimport {makeConnection} from './getConnection';\n//require file to delete/retrieve file from S3\nimport {crudS3Function} from './s3CommonFunction';\n//require file to update status\nimport {changeStatus} from './updateStatusAndUploadFile';\n\n// update the lambda path\nif (process.env.LAMBDA_TASK_ROOT) {\n    process.env['PATH'] = process.env['PATH'] + ':' + process.env['LAMBDA_TASK_ROOT'] + '/bin/';\n    process.env['LD_LIBRARY_PATH'] = process.env['LD_LIBRARY_PATH']+ ':' +process.env['LAMBDA_TASK_ROOT'] +  '/sharedlib/';\n}\n\n// Function to encrypt the pdf with password and upload it in s3 bucket\nmodule.exports.encrypting = async(event, context) => {\n\n    let inputData=event.input;\n    console.log('inputData for encrypt pdf function',inputData);\n    let draftFileName=inputData.draftFileName;\n    let userPassword=inputData.userPassword;\n    let ownerPassword=inputData.ownerPassword;\n    let requestId=inputData.requestId;\n    let payslipId=inputData.payslipId;\n    let orgCode=inputData.orgCode;\n    let addedBy=inputData.addedBy;\n    let emailId=inputData.emailId;\n    let payslipFileName=inputData.payslipFileName;\n    let templateId=inputData.templateId;\n    let employeeName=inputData.employeeName;\n    let payslipMonth=inputData.payslipMonth;\n    let payslipType=inputData.payslipType;\n    let employeeId=inputData.employeeId;\n\n    // file path for creating sample in temp folder\n    let pathOfFileToEncrypt='/tmp/'+orgCode+requestId+payslipFileName;\n    let pathOfEncryptedFile='/tmp/'+orgCode+requestId+'Encrypt'+payslipFileName;\n\n    console.log('Inside createPdf function');\n    try{\n        return new Promise(async function (resolve, reject) {\n        let payslipTemplateData;\n\n        // Get the payslip base64 data from s3 bucket\n        let fileData= await crudS3Function.getS3file(draftFileName,process.env.bucketName)\n        console.log('response after retrieve draft file from s3',fileData)\n        let content=fileData.Body;\n        // Convert buffer to string\n        payslipTemplateData=content.toString('utf8');\n\n        // Remove the draft file uploaded in s3 bucket\n        let removefile= await crudS3Function.deleteFile(draftFileName,process.env.bucketName)\n        console.log('remove draft file',removefile)\n\n        // create the pdf in local path with base64 input\n        fs.writeFile(pathOfFileToEncrypt, payslipTemplateData, {encoding: 'base64'},async function(err,data) {\n            if(err)\n            {\n                console.log('Error in write file in temp folder',err);\n                outputResponse('PBP0103',payslipId,orgCode,requestId,addedBy,templateId);\n            }\n            else{\n                console.log('File created in temp folder',data)\n                // Encrypt the pdf using qpdf\n                const qpdf = spawn('qpdf',\n                ['--encrypt', userPassword, ownerPassword, '256', '--modify=none', '--', pathOfFileToEncrypt, pathOfEncryptedFile]);        \n                qpdf.stderr.on('data', data => {\n                    console.log(`Error from qpdf: ${data}`);\n                    outputResponse('PBP0103',payslipId,orgCode,requestId,addedBy,templateId);\n                });\n                qpdf.on('close', code => {\n                    // If file created successfully 0 is returned\n                    if(code===0){\n                        console.log(`child process exited with code ${code}`);\n                        // upload the pdf in s3 bucket\n                        s3upload(pathOfEncryptedFile,payslipFileName,orgCode,payslipType,payslipMonth,async function(err,data){\n                            if(err){\n                                console.log('Error in upload pdf in s3',err);\n                                // delete the files in temp folder\n                               await deleteTempFile(pathOfFileToEncrypt,pathOfEncryptedFile)  \n                               outputResponse('PBP0104',payslipId,orgCode,requestId,addedBy,templateId);\n                            }\n                            else{\n                                console.log('s3 uploaded successfully',data);\n                                // Delete the files created in temp folder\n                                let deletefile=await deleteTempFile(pathOfFileToEncrypt,pathOfEncryptedFile);\n                                console.log('Response in delete file from tmp folder',deletefile);\n                                // After upload file in S3 update the filename in table\n                                let updateName=await updateFileName(payslipFileName,requestId,payslipId,payslipType,orgCode,addedBy,employeeId)\n                                console.log('Response after updating filename and status',updateName);\n                                if(updateName){\n                                    // validate whether request came for Email request\n                                    if(updateName==='Email')\n                                    {\n                                        // since this is encryption file so encrypted flag is send as 1\n                                        const response = \n                                        {\n                                            string: 'Mail',\n                                            input:{'requestId':requestId,'payslipId':payslipId,'orgCode':orgCode,'addedBy':addedBy,'emailId':emailId,'payslipFileName':payslipFileName,'templateId':templateId,'employeeName':employeeName,'payslipMonth':payslipMonth,'payslipType':payslipType,'Encrypted':1},\n                                            message: 'Pdf generated successfully'\n                                        };\n                                        resolve(response);\n                                    }\n                                    else{\n                                        // for job_type generation move to step1\n                                        const response = \n                                        {\n                                            string: 'Step1',\n                                            input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},\n                                            message: 'Pdf generated successfully'\n                                        };\n                                        resolve(response);\n                                    }\n                                }\n                                else\n                                {\n                                    console.log('error in updating filename in database')\n                                    outputResponse('PBP0001',payslipId,orgCode,requestId,addedBy,templateId);\n                                }\n                            }\n                            })\n                        }\n                        else{\n                            console.log('Error in encrypting payslip');\n                            outputResponse('PBP0103',payslipId,orgCode,requestId,addedBy,templateId);\n                        }\n                });\n            }\n        });\n        \n        // function to return output response\n        function outputResponse(errCode,payslipId,orgCode,requestId,addedBy,templateId){\n            const response = \n            {\n                string: 'ErrorState',\n                input:{'errorCode':errCode,'payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},\n                message: 'Error occured in pdf encryption process'\n            };\n            resolve(response);\n        }\n    })\n    }\n    catch(err){\n        console.log('Error in encrypt pdf catch block',err);\n        let errCode='PBP0103'\n        // if error in retrieve file then delete the file in s3\n        if(err==='PBP0110')\n        {\n            errCode='PBP0110';\n            // Remove the draft file uploaded in s3 bucket\n            let removefile= crudS3Function.deleteFile(draftFileName,process.env.bucketName)\n            console.log('delete draft file in s3',removefile)\n        }\n        const response = \n        {\n            string: 'ErrorState',\n            input:{'errorCode':errCode,'payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},\n            message: 'Error occured in pdf encryption process'\n        };\n        return response;\n    }\n}\n\n// Function to upload the generated payslip pdf in s3 bucket\nasync function s3upload(pathOfEncryptedFile,payslipFilename,orgCode,payslipType,payslipMonth,fn){\n    try{\n        let fileName=pathOfEncryptedFile;\n        // example: payslipmonth:'02,2019' get 2019\n        let payslipYear=payslipMonth.split(',')[1];\n        console.log('inside s3 uploading',fileName);\n        let fileStream = fs.createReadStream(fileName);\n        // Read the encrypted pdf in tmp folder\n        fs.readFile(fileName, (err, data) => {\n            if (err) \n            {\n                console.log('Error in read file function',err)\n                throw err\n            }\n            else{\n                let path=process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+payslipYear+'/'+ payslipType+'/'+payslipFilename;\n                let uploadFileData= crudS3Function.uploadS3file(fileStream,path,process.env.bucketName,'application/pdf')\n                console.log('uploadFileData',uploadFileData)\n                if(uploadFileData)\n                {\n                    console.log('Payslip uploaded successfully',uploadFileData);\n                    fn(null,uploadFileData)\n                }\n                else{\n                    console.log('Error in uploading pdf');\n                    fn('Error',null);\n                }\n            }\n        });\n    }\n    catch(error){\n        console.log('error in uploading file in s3',error);\n        fn(error,null)\n    }\n}\n\n//on uploading s3 delete file in tmp folder\nasync function deleteTempFile(pathOfFileToEncrypt,pathOfEncryptedFile){\n    console.log('delete file function');\n    try{\n        const files = [pathOfFileToEncrypt,pathOfEncryptedFile];\n        files.forEach(function(filePath) {\n            fs.access(filePath, error => {\n                if (!error) {\n                    fs.unlinkSync(filePath,function(error){\n                        if(error){\n                            console.log('error in deleting temp files function',error);  \n                            return '';                   \n                        }                                         \n                    });\n                } else {\n                    console.log('error in accessing files');\n                    return '';\n                }\n            });\n        });\n    }\n    catch(err){\n        console.log('error in deleting files',err);\n        return '';\n    }\n}\n\n// Function to update the Generation_Status as 'Completed' and filename\nasync function updateFileName(fileName,requestId,payslipId,payslipType,orgCode,addedBy,employeeId){\ntry{\n    // make database connection\n    let organizationDb = await makeConnection.createconnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);\n    if(organizationDb){\n    \n    // get the job_type from bulk processing table\n    return(\n        organizationDb('bulk_processing')\n        .select('Job_Type')\n        .where('Request_Id',requestId)\n        .then(async(getJobtype) =>\n        {\n            // Check whether data exist or not\n            if(getJobtype.length>0){\n                let jobType=getJobtype[0].Job_Type;\n\n                // get employee timestamp based on location\n                let employeeTimeZone = await empTimeZoneDetails.getEmployeeTimeZone(addedBy, organizationDb)\n                console.log('employeeTimeZone',employeeTimeZone);\n\n                // update the generation status and filename. since it is encrypted function so update 1 \n                let updateRecord= await changeStatus.updateStatusAndS3Upload(organizationDb,fileName,requestId,payslipId,payslipType,employeeTimeZone,1,employeeId)\n                console.log('response after updating filename and status',updateRecord);\n                if(updateRecord){\n                    return jobType;\n                }\n                else{\n                    return '';\n                }\n            }\n            else{\n                console.log('job type is not available for this request');\n                return '';\n            }\n        })\n        .catch(function(err) {\n            console.log('Err in updating status',err);\n            return '';\n        })\n        .finally(() => {\n            organizationDb.destroy();\n    }))\n    }\n    else{\n        console.log('Error in database connection');\n        return '';\n    }\n}\ncatch(error){\n    console.log('Error in updateFileName catch block',error);\n    organizationDb.destroy();\n    return '';\n}\n}\n", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/getConnection.js", ["75"], "// Require aws-sdk\nconst AWS = require('aws-sdk');\n\nmodule.exports = { createconnection: async(dbSecretName,dbPrefix,orgCode,region) => {\ntry{              \n        // Get secrets from aws secrets manager\n        let client = new AWS.SecretsManager({\n            region: region\n        }); \n\n       // Call function getSecretValue to get secretvalues\n        let secret= await client.getSecretValue({ SecretId: dbSecretName }).promise();\n        let SecretString=JSON.parse(secret.SecretString);\n        let OrganizationDb= {\n                client: 'mysql',\n                connection: {\n                    host: SecretString.hostname,\n                    user: SecretString.username,\n                    password: SecretString.password,\n                    database: dbPrefix + orgCode,\n                    charset: 'utf8'\n                },\n                pool: { min: 1, max: 10 },\n                acquireConnectionTimeout: 10000\n            };\n        //require knex for database connection\n        let organizationDb = require('knex')(OrganizationDb);\n        console.log('organizationDb',organizationDb);\n        return organizationDb;\n    }\n    catch(error){\n        console.log('Error in making database connection',error);\n        return '';\n    }\n    }\n}", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/initiatePdfGeneration.js", ["76"], "'use strict'\n// require the file to get the employee time zone\nimport {empTimeZoneDetails} from './employeetimezone';\n// require file to make database connection\nimport {makeConnection} from './getConnection';\n// require moment package in order the format date in required format\nimport * as moment from 'moment';\n\n// Function to generate pdf along with password protected\n// Pdf filename: Payslip-EmployeeId- MMYYYY (payslip month)- Payslip_10_042019\n// Password for user: First four characters of emp_name + DOB (DDMMYYYY) - abcd30011995\n// Password for admin: Orgcode_organization+ org_start_year. - caprice2019\nmodule.exports.generateEncryptedPdf = async(event, context) => {\n    // variable initialization\n    let inputData=event.input;\n    let requestId=inputData.requestId;\n    let employeeId=inputData.employeeId;\n    let payslipId=inputData.payslipId;\n    let orgCode=inputData.orgCode;\n    let payslipMonth=inputData.payslipMonth;\n    let draftFileName=inputData.draftFileName;\n    let templateId=inputData.templateId;\n    let employeeName=inputData.employeeName;\n    let payslipType=inputData.payslipType;\n    let addedBy=inputData.addedBy;\n\n    try{\n        // get database connection\n        let organizationDb = await makeConnection.createconnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);\n        if(organizationDb){\n\n        // get employee timestamp based on location\n        let employeeTimeZone = await empTimeZoneDetails.getEmployeeTimeZone(addedBy, organizationDb)\n        console.log('response in calculating timezone',employeeTimeZone);\n\n        // form password for encrypting the pdf\n        let pdfEncryption = await encryptionCredentials(organizationDb,payslipId,requestId,employeeId,employeeTimeZone)\n        console.log('response after calling get credentials function',pdfEncryption);\n\n        // If success 4 parameters are returned in response\n        if(Object.keys(pdfEncryption).length===4)\n        {    \n            // example 04,2019 - convert as 042019\n            let definedPayslipMonth= payslipMonth.replace(/[,\\s]/g, '');\n            // example Payslip_10_042019.pdf\n            let payslipFilename=process.env.payslipFileNamePrefix+pdfEncryption.userDefinedId+'_'+definedPayslipMonth+'.pdf';\n            let employeeMail=pdfEncryption.employeeMail;\n            // protect pdf with 2 password\n            let userPassword= pdfEncryption.userPassword;\n            let ownerPassword= pdfEncryption.ownerPassword;\n            console.log('payslip pdf filename',payslipFilename);\n            // destroy the database connection\n            organizationDb.destroy();\n            const response = \n            {\n                string: 'NextStep',\n                input:{'requestId':requestId,'payslipId':payslipId,'orgCode':orgCode,'addedBy':addedBy,'emailId':employeeMail,'payslipFileName':payslipFilename,'templateId':templateId,'employeeName':employeeName,'payslipMonth':payslipMonth,'payslipType':payslipType,'userPassword':userPassword,'ownerPassword':ownerPassword,'draftFileName':draftFileName,'employeeId':employeeId},\n                message: 'Pdf generation initiated successfully'\n            };\n            return response;\n        }\n        else{\n            console.log('Error in retrieve credentials',pdfEncryption);\n            // destroy the database connection\n            organizationDb.destroy();\n            const response = \n            {\n                string: 'ErrorState',\n                input:{'errorCode':pdfEncryption,'payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},\n                message: 'Error occured in pdf generation function'\n            };\n            return response;\n        }\n    }\n    else{\n        console.log('Error in making database connection');\n        const response = \n        {\n          string: 'ErrorState',\n          input:{'errorCode':'_DB0000','payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},\n          message: 'Error occured in pdf generation function'\n        };\n        return response;      \n    }\n    }\n    catch(error){\n        // destroy the database connection\n        organizationDb.destroy();\n        \n        console.log('Err in pdf generation initiation function',error);\n        const response = \n        {\n            string: 'ErrorState',\n            input:{'errorCode':'PBP0102','payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},\n            message: 'Error occured in pdf generation'\n        };\n        return response;\n    }\n}\n\n\n// Function to get the encryption credentials\nasync function encryptionCredentials(organizationDb,payslipId,requestId,employeeId,employeeTimeZone){\n    try{\n        return (\n            organizationDb\n            .transaction(function(trx){\n                return (\n                    // update the Generation_Status as generating for the payslip\n                    organizationDb('payslip_bulk_processing')\n                    .update({ 'Generation_Status': 'Generating','Updated_On':employeeTimeZone })\n                    .where('Payslip_Id',payslipId)\n                    .andWhere('Request_Id',requestId)\n                    .transacting(trx)\n                    .then((updateStatus) =>\n                    {\n                        console.log('Update generation status');\n                        return(\n                            // Get the Dob and name from personal table and empId,DOB from job table\n                            organizationDb('emp_personal_info')\n                            .select('emp_job.User_Defined_EmpId', 'emp_job.Emp_Email','emp_personal_info.Emp_First_Name','emp_personal_info.DOB')\n                            .innerJoin('emp_job', 'emp_personal_info.Employee_Id', 'emp_job.Employee_Id')\n                            .where('emp_job.Employee_Id', employeeId)\n                            .transacting(trx)\n                            .then((getpersonalInfo) =>\n                            {\n                                // Check whether details exists or not\n                                if(getpersonalInfo.length>0)\n                                {\n                                    console.log('get personal Information',getpersonalInfo[0]);\n                                    let empFirstName=getpersonalInfo[0].Emp_First_Name;\n                                    // get first 4 character of emp_name and trim all the spaces\n                                    let definedEmpName= empFirstName.replace(/ /g,'').substring(0,4);\n                                    let empDOB= getpersonalInfo[0].DOB;\n                                    // get DOB of employee in date format\n                                    let splitDob=moment(empDOB).format('DD-MM-YYYY');\n    \n                                    let definedDOB= splitDob.replace(/[-\\s]/g, '');\n                        \n                                    console.log('emp DOB',definedDOB);\n                                    let userDefinedId= getpersonalInfo[0].User_Defined_EmpId;\n                                    let employeeMail =getpersonalInfo[0].Emp_Email;\n                                    // Get the orgcode and start year\n                                    return(\n                                        organizationDb('org_details')\n                                        .select('Org_Code','Start_Year')\n                                        .transacting(trx)\n                                        .then(async(getOrgDetails) =>\n                                        {\n                                            // Check org details exist or not\n                                            if(getOrgDetails.length>0){\n                                                console.log('getOrgDetails',getOrgDetails[0]);\n                                                // get the organization start year\n                                                let startYear= (getOrgDetails[0].Start_Year).split(',');\n                                                console.log('organization start year',startYear);\n                                                let ownerPassword=getOrgDetails[0].Org_Code+startYear[1].trim();\n                                                let userPassword=definedEmpName.toLowerCase()+definedDOB;\n                                                console.log('userPassword',userPassword);\n                                                return ({'userDefinedId':userDefinedId,'employeeMail':employeeMail,'userPassword':userPassword,'ownerPassword':ownerPassword})\n                                            }   \n                                            else{\n                                                console.log('No org details exists')\n                                                throw '_EC0001';  \n                                            }\n                                        }) \n                                    )\n                                }\n                                else{\n                                    console.log('no personal and job details exists')\n                                    throw '_EC0001';  \n                                }\n                            })\n                        )\n                    }\n                    )\n                )\n            })\n            /**return the success result to user */\n            .then(function (result) {\n                return result;\n            })\n            .catch(function(err) {\n                console.log('Err in fetching pdf encryption credentials',err);\n                // Check the error code\n                let errCode=(err==='_EC0001') ? '_EC0001': 'PBP0112'\n                return errCode;\n            })\n        )\n    }\n    catch(error){\n        console.log('Error in retrieve encryption credentials catch block',error);\n        return 'PBP0112';\n    }\n}", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/payslipFormation.js", ["77"], "'use strict'\n// require syncrequest for api call\nlet request = require('syncrequest');\n// require the file to get the employee time zone\nimport {empTimeZoneDetails} from './employeetimezone';\n// require file to bind payslipdata in template\nimport {bindData} from './bindPayslipData';\n// require file to make database connection\nimport {makeConnection} from './getConnection';\n// require file to upload/retrieve data to S3\nimport {crudS3Function} from './s3CommonFunction';\n\n// Function to get payslip data and bind the values in template\nmodule.exports.formPayslipData =async (event, context) => {\n    let bucketName= process.env.bucketName;\n    let inputData=event.input;\n    let orgCode=inputData.orgCode;\n    let payslipId=inputData.payslipId;\n    let payslipType=inputData.payslipType;\n    let templateId=inputData.templateId;\n    let requestId=inputData.requestId;\n    let addedBy=inputData.addedBy;\n    let employeeName=inputData.employeeName;\n    let payslipMonth=inputData.salaryMonth;\n    let employeeId=inputData.employeeId;\n\n    console.log('inputData for formPayslipData function',inputData);\n    try{\n        let payslipTemplate= process.env.domainName +'/'+ orgCode +'/'+ process.env.templatePrefix+'/'+payslipType+'/'+templateId;\n\n        console.log('Path of payslip template',payslipTemplate);\n        \n        // get payslip year\n        let payslipYear=payslipMonth.split(',')[1];\n\n        // get database connection\n        let organizationDb = await makeConnection.createconnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);\n        if(organizationDb){\n        \n        // get timestamp based on employee region\n        let employeeTimeZone = await empTimeZoneDetails.getEmployeeTimeZone(addedBy, organizationDb)\n\n        console.log('employeeTimeZone',employeeTimeZone);\n\n        // Retrieve payslipId,payslipType from payslip_bulk_processing table\n        return(\n            organizationDb\n            .transaction(function(trx){\n                return(\n                    // Update the  generation status as 'Inprogress' in payslip_bulk_processing table\n                    organizationDb('payslip_bulk_processing')\n                    .update({ 'Generation_Status': 'Inprogress','Updated_On':employeeTimeZone })\n                    .where('Payslip_Id',payslipId)\n                    .andWhere('Request_Id',requestId)\n                    .transacting(trx)\n                    .then(async(updateStatus) =>\n                    {\n                        console.log('generation status is updated',updateStatus);\n                        return(\n                            // Get the report creator flag and display address in org details table\n                            organizationDb('org_details')\n                            .select('Show_Report_Creator','Display_Payslip_Address')\n                            .transacting(trx)\n                            .then(async(getOrgData) =>{\n                                let reportCreator=getOrgData[0].Show_Report_Creator;\n                                let displayAddress=getOrgData[0].Display_Payslip_Address;\n                                // Get the payslip template from s3 bucket\n                                let fileData= await crudS3Function.getS3file(payslipTemplate,bucketName)\n                                console.log('get payslip template',fileData)\n                                let content=fileData.Body;\n                                // Convert buffer to string\n                                let templateContent=content.toString('utf8');\n                                // Function to get the payslip data based on payslip Id\n                                let formation=await getPayslipData(templateContent,payslipType,orgCode,payslipId,employeeName,reportCreator,displayAddress)\n                                console.log('formation',formation);\n                                if(!formation)\n                                {\n                                    console.log('error in formation of payslip data');\n                                    const response = \n                                    {\n                                        string: 'ErrorState',\n                                        input:{'errorCode':'PBP0107','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},\n                                        message: 'Error occured in payslip formation function'\n                                    };\n                                    return response;     \n                                }\n                                else\n                                {\n                                    // create a draft file in s3 to store the html content\n                                    let draftFileName='Draft_'+orgCode+requestId+payslipId+'Html.txt';\n                                    let path= process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+payslipYear+'/'+payslipType+'/'+draftFileName;\n                                    let fileData= await crudS3Function.uploadS3file(formation,path,bucketName,'application/txt')\n                                    console.log('upload the draft file in s3',fileData)\n                                    if(fileData){\n                                        const response = \n                                        {\n                                            string: 'CreatePdf',\n                                            input:{'payslipId':payslipId,'requestId':requestId,'employeeId':employeeId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName,'payslipMonth':payslipMonth,'payslipType':payslipType,'draftFileNamePath':path},\n                                            message: 'Payslip formation completed successfullly'\n                                        };\n                                        return response;\n                                    }\n                                    else{\n                                        console.log('Error in uploading template content in s3')\n                                        const response = \n                                        {\n                                            string: 'ErrorState',\n                                            input:{'errorCode':'PBP0104','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},\n                                            message: 'Error occured in payslip formation function'\n                                        };\n                                        return response;\n                                    }\n                                }\n                            })\n                        )\n                    })\n                )\n            })\n            /**return the success result to user */\n            .then(function (result) {\n                return result;\n            })\n            /**catch organizationDbConnection connectivity errors */\n            .catch(function (err) {\n                console.log('error in payslip formation catch block', err);\n                const response = \n                {\n                    string: 'ErrorState',\n                    input:{'errorCode':'PBP0109','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},\n                    message: 'Error occured in payslip formation function'\n                };\n                return response;\n            })\n            .finally(() => {\n                organizationDb.destroy();\n            })\n        )\n        }\n        else{\n            console.log('Error in making database connection');\n            const response = \n            {\n              string: 'ErrorState',\n              input:{'errorCode':'_DB0000','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},\n              message: 'Error occured in payslip formation function'\n            };\n            return response;          \n        }\n    }\n    catch(err){\n        console.log('Error in payslip formation common catch block',err);\n        organizationDb.destroy();\n        const response = \n        {\n            string: 'ErrorState',\n            input:{'errorCode':'PBP0109','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},\n            message: 'Error occured in payslip formation function'\n        };\n        return response;\n    }\n}\n\n// form payslipData and bind with payslip template\nasync function getPayslipData(fileData,payslipType,orgCode,payslipId,employeeName,reportCreator,displayAddress){\n    try{\n        // formation of url to get payslipdata based on type\n        let formPayslipData=(payslipType==='Monthly')?process.env.getMonthlyPayslipdata:process.env.getHourlyPayslipdata;\n        let header=(process.env.domainName==='upshotindia')?'http:':'https:'\n        let url= header+'//'+orgCode+'.'+process.env.domainName+process.env.webAddress+'/'+formPayslipData+'/'+payslipId;\n        console.log('Request url',url);\n        let getPayslipData = request.sync(url);\n        console.log('Payslip data after ajax call',getPayslipData);\n        if(getPayslipData){\n            let result= JSON.parse(getPayslipData.response.body);\n            console.log('payslip data',result);\n            // bind the payslipdata along with template\n            let bindPayslipValue = await bindData.formpayslip(fileData,result,payslipType,employeeName,reportCreator,displayAddress);\n            console.log('Response from bindPayslipValue function',bindPayslipValue);   \n            if(!bindPayslipValue){\n                console.log('error in binding value function');\n                return '';\n            }\n            else{\n                return bindPayslipValue;   \n            }\n        }\n        else{\n            console.log('Error in retrieve payslip data');\n            return '';\n        }   \n    }\n    catch(err){\n        console.log('Error in getPayslipData function catch block',err);\n        return '';\n    }\n}\n", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/sendMail.js", ["78"], "'use strict'\n// require aws-sdk to use aws services\nconst AWS = require('aws-sdk');\n// require ses for sending mail\nlet ses = new AWS.SES({\n    region: process.env.sesTemplatesRegion });\n// require nodemailer for sending mail with attachment\nlet nodemailer = require('nodemailer');\n// require the file to get the employee time zone\nimport {empTimeZoneDetails} from './employeetimezone';\n// require file to make database connection\nimport {makeConnection} from './getConnection';\n// require file to get data from S3\nimport {crudS3Function} from './s3CommonFunction';\n// require handlebars to bind value in template\nconst handlebars=require('handlebars')\n// require fs package\nconst fs=require('fs');\n// Create object for s3 bucket\nconst S3 = new AWS.S3();\n\n// Function to email the payslip as a attachment\nmodule.exports.emailCommunication = async(event, context) => {\n    let inputData= event.input;\n    console.log('inputData for mail communication',inputData);\n\n    let requestId=inputData.requestId;\n    let payslipId=inputData.payslipId;\n    let orgCode=inputData.orgCode;\n    let fileName=inputData.payslipFileName;\n    let emailId = inputData.emailId;\n    let templateId=inputData.templateId;\n    let employeeName=inputData.employeeName;\n    let payslipMonth=inputData.payslipMonth;\n    let payslipType=inputData.payslipType;\n    let Encrypted=inputData.Encrypted;\n    let addedBy=inputData.addedBy;\n\n    // For calculating the salary month(In mail subject)\n    let months = [\n        'January',\n        'February',\n        'March',\n        'April',\n        'May',\n        'June',\n        'July',\n        'August',\n        'September',\n        'October',\n        'November',\n        'December'\n        ];\n          \n    try{\n        let organizationName;\n\n        // get database connection\n        let organizationDb = await makeConnection.createconnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);\n        if(organizationDb){\n            try{\n            // get timezone based on employee location\n            let employeeTimeZone = await empTimeZoneDetails.getEmployeeTimeZone(addedBy, organizationDb)\n        \n            // Update the status in payslip_bulk_processing table\n            let databaseUpdation = await statusUpdate(organizationDb,payslipId,requestId,employeeTimeZone,'Inprogress')\n            console.log('response in updating inprogress status',databaseUpdation);\n            \n            organizationName= await getOrganizationDetails(organizationDb,orgCode);\n            console.log('response from getting Organization Details',organizationName);\n            // In case of error only one param is returned so it will be handled in else part\n            if(Object.keys(organizationName).length>1){\n                // Form subject for mail as orgname+payslip+month(in words)\n                let salaryMonth=payslipMonth.split(',')[0];\n                let salaryYear=payslipMonth.split(',')[1];\n                // Get the month in words\n                let month_index = parseInt(salaryMonth) - 1;\n\n                let payslipMonthInWords=months[month_index];\n                // formation of mail subject\n                let mailSubject= organizationName.orgname+' ' + process.env.mailSubject+' '+ payslipMonthInWords +' '+salaryYear;\n                console.log('formation of mailSubject',mailSubject);\n\n                // formation of s3 url path\n                let path= process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+salaryYear+'/'+payslipType+'/'+fileName;\n\n                // function to get the file from s3\n                let getData=await crudS3Function.getS3file(path,process.env.bucketName)\n                console.log('response after getting s3 file',getData);\n                if(getData){\n                    // get the mail template and bind the values in template \n                    let mailData=await getEmailTemplate(organizationName,mailSubject,payslipMonthInWords,salaryYear,emailId,getData,fileName,employeeName,Encrypted)\n                    console.log('response from email template function',mailData);\n                    if(mailData==='success'){\n                        // update the Email_Status as 'completed'\n                        let databaseUpdation = await statusUpdate(organizationDb,payslipId,requestId,employeeTimeZone,'Completed')\n                        console.log('response from status update function',databaseUpdation);\n                        const response = \n                        {\n                            string: 'Step1',\n                            input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},\n                            message: 'Email send successfully'\n                        };\n                        // destroy database connection\n                        organizationDb.destroy();\n                        return response;\n                    }\n                    else{\n                        organizationDb.destroy();\n                        // return error response\n                        let response= await outputResponse(mailData,payslipId,requestId,orgCode,templateId,employeeName,addedBy);\n                        return response;\n                    }\n                }            \n                else   \n                {         \n                    // err in retrieve file from s3\n                    console.log('Error getting attachment from S3');\n                    organizationDb.destroy();\n                    // return error response\n                    let response= await outputResponse('PBP0110',payslipId,requestId,orgCode,templateId,employeeName,addedBy);\n                    return response;\n                }\n            }\n            else{\n                console.log('Error in retrieve organization details');\n                organizationDb.destroy();\n                // return error response\n                let response= await outputResponse(organizationName.errCode,payslipId,requestId,orgCode,templateId,employeeName,addedBy);\n                return response;\n            }\n        }\n        catch(err){\n            console.log('Error in send mail function catch block after database connection',err);\n            organizationDb.destroy();\n            // return error response\n            let response= await outputResponse('PBP0114',payslipId,requestId,orgCode,templateId,employeeName,addedBy);\n            return response;\n        }\n        }\n        else{\n            console.log('Error in making database connection');\n            // return error response\n            let response= await outputResponse('_DB0000',payslipId,requestId,orgCode,templateId,employeeName,addedBy);\n            return response;    \n        }\n    }\n    catch(error){\n        console.log('error in mail commmunication common catch block',error);\n        let errCode=(error==='PBP0110')?'PBP0110':'PBP0114';\n        // return error response\n        let response= await outputResponse(errCode,payslipId,requestId,orgCode,templateId,employeeName,addedBy);\n        return response;\n    }\n};\n\n// return output error response\nfunction outputResponse(errCode,payslipId,requestId,orgCode,templateId,employeeName,addedBy){\n    const response = \n    {\n        string: 'ErrorState',\n        input:{'errorCode':errCode,'payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName,'source':'mail'},\n        message: 'Error in mail communication'\n    };\n    return response;\n}\n\n// Function to send mail\nfunction sendMail(transporter,mailOptions)\n{\n    console.log('inside sendmail function');\n    return new Promise(function (resolve, reject) {\n        // send email\n        transporter.sendMail(mailOptions, function (err, info) {\n            if (err) {\n                console.log('Error sending email',err);\n                resolve('PBP0105');\n            } else {\n                console.log('mail send successfully')\n                resolve('success');\n            }\n        });\n    });\n}\n\n// update the emailStatus in payslip_bulk_processing table\nasync function statusUpdate(organizationDb,payslipId,requestId,employeeTimeZone,status){\n    console.log('Inside statusUpdate',organizationDb,payslipId,requestId,employeeTimeZone,status);\n    return (\n        organizationDb('payslip_bulk_processing')\n        .update({ 'Email_Status': status,'Updated_On':employeeTimeZone })\n        .where('Payslip_Id',payslipId)\n        .andWhere('Request_Id',requestId)\n        .then((updateStatus) =>\n        {\n            console.log('status updated successfully',updateStatus)\n            return '';\n        })\n        .catch(function(err) {\n            console.log('Error in statusUpdate function',err);\n            throw 'PBP0001';\n        })\n    )\n}\n\n// Get organization details in database for sending email template\nasync function getOrganizationDetails(organizationDb,orgCode){\n    try{\n        return (\n            // get the org_name and address from org details and location table\n            organizationDb('location')\n            .select('location.Street1', 'location.Street2', 'location.Pincode', 'state.State_Name', 'country.Country_Name', 'city.City_Name','org_details.Org_Name','org_details.HR_Admin_Email_Address','org_details.Report_LogoPath')\n            .from('location')\n            .leftJoin('country', 'location.Country_Code','country.Country_Code')\n            .leftJoin('state', 'location.State_Id','state.State_Id')\n            .leftJoin('city', 'location.City_Id', 'city.City_Id')\n            .innerJoin('org_details')\n            .where('location.Location_Type','MainBranch')\n            .then(async(getorgdetails) =>\n            {\n                console.log('getorgdetails',getorgdetails)\n                // Check whether data exist or not\n                if(getorgdetails.length>0){\n                    let url;\n\n                    let OrgName=getorgdetails[0].Org_Name;\n                    let street1=getorgdetails[0].Street1;\n                    let street2=getorgdetails[0].Street2;\n                    let city=getorgdetails[0].City_Name;\n                    let country=getorgdetails[0].Country_Name;\n                    let state=getorgdetails[0].State_Name;\n                    let pincode=getorgdetails[0].Pincode;\n                    let hrEmail=getorgdetails[0].HR_Admin_Email_Address;\n                    let orgLogo=getorgdetails[0].Report_LogoPath;\n                    // If logo exist get the presigned url\n                    if(orgLogo)\n                    {\n                        let filePath = process.env.domainName + '_upload/' + orgCode+ '_tmp/logos/' + orgLogo;\n                        let bucket = process.env.logoBucket; // HRAPP report logo bucket\n\n                        // Call function headObject to check files exists or not in s3 bucket. Pass bucket name and file name as input\n                        await S3.headObject({ Bucket: bucket, Key: filePath }).promise();\n\n                        // Call function getSignedUrl and pass getObject/putObject function to getpresigned url\n                        let reportLogoS3Path = S3.getSignedUrl('getObject', { Bucket: bucket, Key: filePath});\n                        url= reportLogoS3Path.split('?')[0];\n                        console.log('url',url);\n                    }\n                    return ({'orgname':OrgName,'street1':street1,'street2':street2,'city':city,'country':country,'state':state,'pincode':pincode,'hrEmail':hrEmail,'orgLogo':url})\n                }\n                else{\n                    console.log('No organization exists');\n                    return {'errCode':'_EC0001'};\n                }\n            })\n            .catch(function(err) {\n                console.log('Err in getting organization details',err);\n                return {'errCode':'PBP0002'};\n            })\n        )\n    }\n    catch(error){\n        console.log('error in getOrganizationDetails catch blocl',error);\n        return {'errCode':'PBP0002'};\n    }\n}\n\n// get the email template and bind the content. Send mail along with template\nasync function getEmailTemplate(organizationName,mailSubject,payslipMonthInWords,salaryYear,emailId,getData,fileName,employeeName,Encrypted){\n    return new Promise(function (resolve, reject) {\n    try{\n        // if file is encrypted need to send the template along with password hint \n        let htmlFile=(Encrypted)?'templateWithPassword.html':'templateWithoutPassword.html';\n        // get the payslip template content from this file\n        fs.readFile(htmlFile,async function(err, templatedata) {\n            if(err){\n                console.log('Error in reading mail template content',err);\n                resolve( 'PBP0113');\n            }\n            else{ \n                console.log('templatedata',templatedata);\n                // complile the template with data\n                let template = handlebars.compile(templatedata.toString('utf8'));\n                // pass this params to html template and bind this values\n                let replacements = {\n                    hrEmail:organizationName.hrEmail?organizationName.hrEmail:'',\n                    orgLogo:organizationName.orgLogo,\n                    month:payslipMonthInWords,\n                    year:salaryYear,\n                    companyName:organizationName.orgname,\n                    employeeName:employeeName,\n                    street1:organizationName.street1,\n                    street2:organizationName.street2,\n                    city:organizationName.city,\n                    pinCode:organizationName.pincode,\n                    state:organizationName.state,\n                    country:organizationName.country\n                };\n                // replace the values in template with these params\n                let htmlToSend = template(replacements);\n                console.log('replacements',replacements); \n\n                let mailOptions = {\n                    from: process.env.fromAddress,\n                    subject: mailSubject,\n                    html: htmlToSend,\n                    to: emailId,\n                    attachments: [\n                        {\n                            filename:fileName,\n                            content: getData.Body\n                        }\n                    ]\n                };\n        \n                // create Nodemailer SES transporter\n                let transporter = nodemailer.createTransport({\n                    SES: ses\n                });\n                // function to mail the content\n                let sendEmailData= await sendMail(transporter,mailOptions);\n                console.log('sendEmailData',sendEmailData);\n                if(sendEmailData==='success'){\n                    console.log('Email sent successfully');\n                    resolve ('success');\n                }\n                else{\n                    resolve('PBP0105');\n                }\n            }\n        })\n    }\n    catch(err){\n        console.log('error in getEmailTemplate function',err);\n        resolve('PBP0105')\n    }\n    })\n}", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/updateStatus.js", ["79"], "'use strict'\n// require the file to get the employee time zone\nimport {empTimeZoneDetails} from './employeetimezone';\n// require the file to update the status\nimport {updateJobStatus} from './updateJobStatus';\n// require file to make database connection\nimport { makeConnection} from './getConnection';\n\n\n// Function to update the status in table\nmodule.exports.updateStatus = async(event, context) => {\ntry{\n    let inputData=event.input;\n    let payslipId=inputData.payslipId;\n    let requestId=inputData.requestId;\n    let orgCode=inputData.orgCode;\n    let errorCode=inputData.errorCode;\n    let templateId=inputData.templateId;\n    let addedBy=inputData.addedBy;\n\n    // Check whether error returned from mail function\n    let emailStatus=(inputData.source=='mail')?true:false;\n\n    console.log('inputData in update status function',inputData);\n\n    // If step 1 if the inputs are empty return the failure response.\n    if(errorCode==='IVE0000')\n    {\n        const response = \n        {\n            string: 'ErrorState',\n            input:{'errorCode':'IVE0000'},\n            message: 'Error in updateStatus function'\n        };\n        return response;\n    }\n\n    // get database connection\n    let organizationDb = await makeConnection.createconnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);\n    if(organizationDb){\n        try{\n            // get employee timestamp based on location\n            let employeeTimeZone = await empTimeZoneDetails.getEmployeeTimeZone(addedBy, organizationDb)\n            console.log('response from calculate timezone',employeeTimeZone);\n\n            // if error occurs in mail function update the 'email status' otherwise update the generation status and email status\n            if(emailStatus){\n                return(\n                    // update the emial_status as 'Failure',error_code\n                    organizationDb('payslip_bulk_processing')\n                    .update({ 'Email_Status' : 'Failure', 'Error_Code':errorCode,'Updated_On':employeeTimeZone })\n                    .where('Payslip_Id',payslipId)\n                    .andWhere('Request_Id',requestId)\n                    .then(async(updateStatus) =>\n                    {\n                        console.log('update email status as failure',updateStatus);\n                        // function to return the output response\n                        let returnOutput = await outputResponse(organizationDb,requestId,employeeTimeZone,templateId,orgCode,'Email');\n                        console.log('response from output response',returnOutput);\n                        if(returnOutput){\n                            return returnOutput;\n                        }\n                        else{\n                            const response = \n                            {\n                                string: 'ErrorState',\n                                input:{'errorCode':'PBP0001'},\n                                message: 'Error in updateStatus function'\n                            };\n                            return response;\n                        }\n                    })\n                    .catch(function(err) {\n                        console.log('Err in updating email status',err);\n                        const response = \n                        {\n                            string: 'ErrorState',\n                            input:{'errorCode':'PBP0001'},\n                            message: 'Error in updateStatus function'\n                        };\n                        return response;\n                    })\n                    .finally(() => {\n                        organizationDb.destroy();\n                    })\n                )\n            }\n            else\n            {\n                // get the job type for this requestId\n                let getType = await getJobType(organizationDb,requestId)\n                console.log('response after calling getjobtype function',getType);\n                if(getType){\n                    // if job type is email update both generation and email status as 'failure'\n                    let updateDetails=(getType=='Email')?{ 'Generation_Status' : 'Failure', 'Email_Status' : 'Failure','Error_Code':errorCode,'Updated_On':employeeTimeZone }:{ 'Generation_Status' : 'Failure', 'Error_Code':errorCode,'Updated_On':employeeTimeZone };\n                    return(\n                        // update the generation_status as 'Failure' and Error_code\n                        organizationDb('payslip_bulk_processing')\n                        .update(updateDetails)\n                        .where('Payslip_Id',payslipId)\n                        .andWhere('Request_Id',requestId)\n                        .then(async(updateStatus) =>\n                        {\n                            console.log('updating generation and email status',updateStatus);\n                            // function to return the response\n                            let returnOutput = await outputResponse(organizationDb,requestId,employeeTimeZone,templateId,orgCode,getType);\n                            console.log('response from output response',returnOutput);\n                            if(returnOutput){\n                                // destroy database connection\n                                organizationDb.destroy();\n                                return returnOutput;\n                            }\n                            else{\n                                // destroy database connection\n                                organizationDb.destroy();\n                                const response = \n                                {\n                                    string: 'ErrorState',\n                                    input:{'errorCode':'PBP0001'},\n                                    message: 'Error in updateStatus function'\n                                };\n                                return response;\n                            }\n                        })\n                        .catch(function(err) {\n                            console.log('Err in updating generation and email status',err);\n                            const response = \n                            {\n                                string: 'ErrorState',\n                                input:{'errorCode':'PBP0001'},\n                                message: 'Error in updateStatus function'\n                            };\n                            // destroy database connection\n                            organizationDb.destroy();\n                            return response;\n                        })\n                    )\n                }\n                else{\n                    console.log('error in retrieve job type');\n                    const response = \n                    {\n                        string: 'Step1',\n                        input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},\n                        message: 'Move to payslip generation step'\n                    };\n                    // destroy database connection\n                    organizationDb.destroy();\n                    return response; \n                }\n            }\n        }\n        catch(error){\n            console.log('Error in common catch block after database connection',error);\n            const response = \n            {\n                string: 'ErrorState',\n                input:{'errorCode':'_DB0000'},\n                message: 'Error in updateStatus function'\n            };\n            // destroy database connection\n            organizationDb.destroy();\n            return response;  \n        }\n    }\n    else{\n        console.log('Error in making database connection');\n        const response = \n        {\n            string: 'ErrorState',\n            input:{'errorCode':'_DB0000'},\n            message: 'Error in updateStatus function'\n        };\n        return response;      \n    }\n}\ncatch(error)\n{\n    console.log('Error in update status function catch block',error);\n    const response = \n    {\n        string: 'ErrorState',\n        input:{'errorCode':'PBP0001'},\n        message: 'Error in updateStatus function'\n    };\n    return response;\n}\n};\n\n// function to get jobtype based on requestId\nasync function getJobType(organizationDb,requestId){\n    return(\n        organizationDb('bulk_processing')\n        .select('Job_Type')\n        .where('Request_Id',requestId)\n        .then((getrecord) =>\n        {\n            if(getrecord.length>0){\n                let jobType=getrecord[0].Job_Type;\n                return jobType;\n            }\n            else\n            {\n                console.log('No jobtype exist for this request');\n                return '';\n            }\n\n        })\n        .catch(function(err) {\n            console.log('Error in retrieve job type',err);\n            return '';\n        })\n    )\n}\n\n// Function to return the output response based on condition\nasync function outputResponse(organizationDb,requestId,employeeTimeZone,templateId,orgCode,getType){\n    try{\n        // update the job status if all records are processed\n        let status = await updateJobStatus.updateJobStatus(organizationDb,'Completed',employeeTimeZone,requestId,getType)\n        console.log('update completed status',status);\n        if(status=='completed')\n        {\n            const response = \n            {\n                string: 'Success',\n                message: 'Step function completed'\n            };\n            return response;\n        }\n        else{\n            const response = \n            {\n                string: 'Step1',\n                input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},\n                message: 'Move to payslip generation step'\n            };\n            return response; \n        }\n    }\n    catch(error){\n        console.log('error in outputResponse catch block',error);\n        const response = \n        {\n            string: 'Step1',\n            input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},\n            message: 'Move to payslip generation step'\n        };\n        return response; \n    }\n}", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/employeetimezone.js", ["80", "81", "82", "83", "84", "85", "86"], "'use strict'\n// require moment package to format time in required format\nconst Moment = require('moment-timezone');\n\nmodule.exports = {\n    getEmployeeTimeZone: (employeeId, organizationDbConnection) => {\n        // Get current time based on employee time zone by using employeeId\n        return (\n            organizationDbConnection\n                .transaction(function(trx) {\n                    return organizationDbConnection\n                        .select('*')\n                        .from('emp_job')\n                        .leftJoin('location', 'emp_job.Location_Id', 'location.Location_Id')\n                        .innerJoin('timezone', 'location.Zone_ID', 'timezone.Zone_ID')\n                        .where('Employee_Id', employeeId)\n                        .transacting(trx)\n                        .then(empTimeZone => {\n                            /** employee timestamp in YYYY-MM-DD HH:mm:ss */\n                            const timestamp = Moment()\n                                .tz(empTimeZone[0].TimeZone_Id)\n                                .format('YYYY-MM-DD HH:mm:ss');\n                            return timestamp;\n                        })\n                        .then(trx.commit) /** commit all the transactions */\n                        .catch(trx.rollback); /** rollback if any error occurs */\n                })\n                /** return the employee time zone to the calling function*/\n                .then(function(result) {\n                    return result;\n                })\n                /** check and return if any error occured */\n                .catch(function(err) {\n                    if (err.code === 'ECONNREFUSED') {\n                        console.log('missing database connection', err);\n                        return '';\n                    } else {\n                        console.log('error in database connection', err);\n                        return '';\n                    }\n                })\n        );\n    }\n};\n", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/prettier.config.js", [], "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/s3CommonFunction.js", ["87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101"], "'use strict'\n// require aws-sdk to use aws services\nconst AWS = require('aws-sdk');\n// Create object for s3 bucket\nconst S3 = new AWS.S3();\n\nmodule.exports = {\n    // Function to upload file in s3 bucket\n    uploadS3file: (data,path,bucket,contenttype) => {\n        return new Promise(function (resolve, reject) {\n            const params = {\n                Bucket: bucket,\n                Key: path,\n                Body: data,\n                ContentType: contenttype\n            };\n            console.log('params',params);\n            S3.putObject(params, function(s3Err, s3Data) {\n                if (s3Err)\n                {\n                    console.log('Error in uploading file in s3',s3Err);\n                    resolve('');\n                }\n                else{\n                    console.log('File uploaded in bucket',s3Data);\n                    resolve(s3Data);\n                }\n            });\n        })\n    },\n    // Delete file from s3 bucket\n    deleteFile: (path,bucket) => {\n        return new Promise(function (resolve, reject) {\n            const params = {\n            Bucket: bucket,\n            Key: path,\n        };\n        S3.deleteObject(params, function (err, data) {\n            if (data) {\n                console.log('File deleted successfully in s3 bucket',data);\n                resolve('');\n            }\n            else {\n                console.log('Error in deleting file from s3 ',err);\n                resolve('');\n            }\n        });\n    });\n    },\n    // S3 file retrieve function\n    getS3file: (key,bucket) => {\n        return new Promise(function (resolve, reject) {\n            S3.getObject(\n                {\n                    Bucket: bucket,\n                    Key: key\n                },\n                function (err, data) {\n                    if (err) \n                    {\n                        console.log('Error in getting file from s3',err);\n                        reject('PBP0110');\n                    }\n                    else {\n                        console.log('File retrieved from s3 bucket',data);\n                        resolve (data);\n                    }\n                }\n            );\n        })\n    }\n\n}\n\n", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/updateJobStatus.js", ["102", "103", "104", "105", "106", "107", "108", "109"], "'use strict'\n// Function to update the job status as completed in bulk_processing table if all the records are processed\nmodule.exports = { \n    updateJobStatus: (organizationDb,status,employeeTimeZone,requestId,jobType) => {\n        console.log('update job',organizationDb,status,employeeTimeZone,requestId,jobType);\n        try{\n            // if job type is email check generation and email status\n            const checkBothStatus= organizationDb.raw('update bulk_processing set Job_Status=\"'+status+'\",Updated_On= \"'+employeeTimeZone+'\" where Request_Id=\"'+requestId+'\" and (select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id=\"'+requestId+'\" and ((PBP.Email_Status=\"Completed\" or PBP.Email_Status=\"Failure\") and (PBP.Generation_Status=\"Completed\" or PBP.Generation_Status=\"Failure\" or PBP.Generation_Status=\"Already Generated\")))=(select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id=\"'+requestId+'\")');\n            const checkGenerationStatus=organizationDb.raw('update bulk_processing set Job_Status=\"'+status+'\",Updated_On= \"'+employeeTimeZone+'\" where Request_Id=\"'+requestId+'\" and (select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id=\"'+requestId+'\" and ((PBP.Generation_Status=\"Completed\" or PBP.Generation_Status=\"Failure\" or PBP.Generation_Status=\"Already Generated\")))=(select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id=\"'+requestId+'\")');\n            // If job type is generation check generation_status else check both generation and email status\n            const subQuery=(jobType==='Email')?checkBothStatus:checkGenerationStatus;\n            return(\n                subQuery                \n                .then((updatejobStatus) =>\n                {\n                    console.log('updatejobStatus',updatejobStatus[0].affectedRows)\n                    if(updatejobStatus[0].affectedRows){\n                        return 'completed';}\n                    else{\n                        return '';\n                    }\n                })\n                .catch(function(err) {\n                    console.log('Err in updating job status',err);\n                    return '';\n                })\n            )\n        }\n        catch(err){\n            console.log('Error in updateJobstatus function',err);\n            return '';\n        }\n    }\n}\n", "/home/<USER>/Documents/Connected banking/connectedbanking/bulk-payslip-processing/updateStatusAndUploadFile.js", ["110", "111", "112", "113", "114", "115", "116", "117", "118"], "\n'use strict'\nmodule.exports = {\n    updateStatusAndS3Upload: (organizationDb,fileName,requestId,payslipId,payslipType,employeeTimeZone,isEncrypted,employeeId) => {\n        try{        \n        console.log('Inputs:',fileName,requestId,payslipId,payslipType,employeeTimeZone,isEncrypted,employeeId)\n            // Based on type get the payslip table name\n            const payslipTable = (payslipType==='Monthly') ? 'salary_payslip' : 'hourlywages_payslip';\n            \n            // update the generation status as completed\n            return(\n                organizationDb('payslip_bulk_processing')\n                .update({ 'Generation_Status': 'Completed','Updated_On':employeeTimeZone })\n                .where('Payslip_Id',payslipId)\n                .andWhere('Request_Id',requestId)\n                .then((updateStatus) =>\n                {\n                    console.log('Status updated in payslip bulk processing table');\n                    return(\n                        // update the filename and S3_File_Encrypted flag in payslip table\n                        organizationDb(payslipTable)\n                        .update({ 'S3_FileName': fileName, 'S3_File_Encrypted':isEncrypted })\n                        .where('Payslip_Id',payslipId)\n                        .andWhere('Employee_Id',employeeId)\n                        .then((updateStatus) =>{\n                            console.log('Filename updated in payslip table');\n                            return 'success';\n                        }\n                        )\n                    )\n                })\n                .catch(function(err) {\n                    console.log('Err in updating status',err);\n                    return '';\n                })\n            )\n        }\n        catch(error){\n            console.log('Error in updateFileName and status catch block',error);\n            return '';\n        }\n    }\n};\n", {"ruleId": null, "fatal": true, "severity": 2, "message": "119", "line": 6, "column": 95}, {"ruleId": null, "fatal": true, "severity": 2, "message": "120", "line": 3, "column": 1}, {"ruleId": null, "fatal": true, "severity": 2, "message": "120", "line": 5, "column": 1}, {"ruleId": null, "fatal": true, "severity": 2, "message": "120", "line": 10, "column": 1}, {"ruleId": null, "fatal": true, "severity": 2, "message": "119", "line": 4, "column": 82}, {"ruleId": null, "fatal": true, "severity": 2, "message": "120", "line": 3, "column": 1}, {"ruleId": null, "fatal": true, "severity": 2, "message": "120", "line": 5, "column": 1}, {"ruleId": null, "fatal": true, "severity": 2, "message": "120", "line": 10, "column": 1}, {"ruleId": null, "fatal": true, "severity": 2, "message": "120", "line": 3, "column": 1}, {"ruleId": "121", "severity": 2, "message": "122", "line": 10, "column": 30, "nodeType": "123", "endLine": 27, "endColumn": 18, "fix": "124"}, {"ruleId": "125", "severity": 2, "message": "126", "line": 20, "column": 47, "nodeType": "127", "messageId": "128"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 29, "column": 23, "nodeType": "123", "endLine": 31, "endColumn": 18, "fix": "129"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 33, "column": 24, "nodeType": "123", "endLine": 41, "endColumn": 18, "fix": "130"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 35, "column": 25, "nodeType": "133", "messageId": "134", "endLine": 35, "endColumn": 36}, {"ruleId": "135", "severity": 2, "message": "136", "line": 37, "column": 28, "nodeType": "137", "messageId": "134", "endLine": 40, "endColumn": 22, "fix": "138"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 38, "column": 25, "nodeType": "133", "messageId": "134", "endLine": 38, "endColumn": 36}, {"ruleId": "121", "severity": 2, "message": "122", "line": 10, "column": 28, "nodeType": "123", "endLine": 29, "endColumn": 10, "fix": "139"}, {"ruleId": "140", "severity": 2, "message": "141", "line": 10, "column": 47, "nodeType": "142", "endLine": 10, "endColumn": 53}, {"ruleId": "131", "severity": 2, "message": "132", "line": 17, "column": 13, "nodeType": "133", "messageId": "134", "endLine": 17, "endColumn": 24}, {"ruleId": "121", "severity": 2, "message": "122", "line": 18, "column": 34, "nodeType": "123", "endLine": 28, "endColumn": 14, "fix": "143"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 21, "column": 21, "nodeType": "133", "messageId": "134", "endLine": 21, "endColumn": 32}, {"ruleId": "131", "severity": 2, "message": "132", "line": 25, "column": 21, "nodeType": "133", "messageId": "134", "endLine": 25, "endColumn": 32}, {"ruleId": "121", "severity": 2, "message": "122", "line": 33, "column": 28, "nodeType": "123", "endLine": 48, "endColumn": 6, "fix": "144"}, {"ruleId": "140", "severity": 2, "message": "141", "line": 33, "column": 47, "nodeType": "142", "endLine": 33, "endColumn": 53}, {"ruleId": "121", "severity": 2, "message": "122", "line": 38, "column": 33, "nodeType": "123", "endLine": 47, "endColumn": 10, "fix": "145"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 40, "column": 17, "nodeType": "133", "messageId": "134", "endLine": 40, "endColumn": 28}, {"ruleId": "131", "severity": 2, "message": "132", "line": 44, "column": 17, "nodeType": "133", "messageId": "134", "endLine": 44, "endColumn": 28}, {"ruleId": "121", "severity": 2, "message": "122", "line": 52, "column": 28, "nodeType": "123", "endLine": 70, "endColumn": 10, "fix": "146"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 58, "column": 17, "nodeType": "123", "endLine": 68, "endColumn": 18, "fix": "147"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 61, "column": 25, "nodeType": "133", "messageId": "134", "endLine": 61, "endColumn": 36}, {"ruleId": "131", "severity": 2, "message": "132", "line": 65, "column": 25, "nodeType": "133", "messageId": "134", "endLine": 65, "endColumn": 36}, {"ruleId": "131", "severity": 2, "message": "132", "line": 5, "column": 9, "nodeType": "133", "messageId": "134", "endLine": 5, "endColumn": 20}, {"ruleId": "148", "severity": 2, "message": "149", "line": 8, "column": 55, "nodeType": "150", "endLine": 8, "endColumn": 574, "fix": "151"}, {"ruleId": "148", "severity": 2, "message": "149", "line": 9, "column": 60, "nodeType": "150", "endLine": 9, "endColumn": 514, "fix": "152"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 16, "column": 21, "nodeType": "133", "messageId": "134", "endLine": 16, "endColumn": 32}, {"ruleId": "135", "severity": 2, "message": "136", "line": 19, "column": 25, "nodeType": "137", "messageId": "134", "endLine": 21, "endColumn": 22, "fix": "153"}, {"ruleId": "121", "severity": 2, "message": "122", "line": 23, "column": 24, "nodeType": "123", "endLine": 26, "endColumn": 18, "fix": "154"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 24, "column": 21, "nodeType": "133", "messageId": "134", "endLine": 24, "endColumn": 32}, {"ruleId": "131", "severity": 2, "message": "132", "line": 30, "column": 13, "nodeType": "133", "messageId": "134", "endLine": 30, "endColumn": 24}, {"ruleId": "131", "severity": 2, "message": "132", "line": 6, "column": 9, "nodeType": "133", "messageId": "134", "endLine": 6, "endColumn": 20}, {"ruleId": "140", "severity": 2, "message": "155", "line": 16, "column": 24, "nodeType": "142", "endLine": 16, "endColumn": 36}, {"ruleId": "131", "severity": 2, "message": "132", "line": 18, "column": 21, "nodeType": "133", "messageId": "134", "endLine": 18, "endColumn": 32}, {"ruleId": "156", "severity": 2, "message": "157", "line": 25, "column": 32, "nodeType": "142", "endLine": 25, "endColumn": 44}, {"ruleId": "140", "severity": 2, "message": "155", "line": 25, "column": 32, "nodeType": "142", "endLine": 25, "endColumn": 44}, {"ruleId": "131", "severity": 2, "message": "132", "line": 26, "column": 29, "nodeType": "133", "messageId": "134", "endLine": 26, "endColumn": 40}, {"ruleId": "121", "severity": 2, "message": "122", "line": 32, "column": 24, "nodeType": "123", "endLine": 35, "endColumn": 18, "fix": "158"}, {"ruleId": "131", "severity": 2, "message": "132", "line": 33, "column": 21, "nodeType": "133", "messageId": "134", "endLine": 33, "endColumn": 32}, {"ruleId": "131", "severity": 2, "message": "132", "line": 39, "column": 13, "nodeType": "133", "messageId": "134", "endLine": 39, "endColumn": 24}, "Parsing error: Unexpected token =>", "Parsing error: 'import' and 'export' may appear only with 'sourceType: module'", "prefer-arrow-callback", "Unexpected function expression.", "FunctionExpression", {"range": "159", "text": "160"}, "new-cap", "A function with a name starting with an uppercase letter should only be used as a constructor.", "CallExpression", "upper", {"range": "161", "text": "162"}, {"range": "163", "text": "164"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", "no-else-return", "Unnecessary 'else' after 'return'.", "BlockStatement", {"range": "165", "text": "166"}, {"range": "167", "text": "168"}, "no-unused-vars", "'reject' is defined but never used.", "Identifier", {"range": "169", "text": "170"}, {"range": "171", "text": "172"}, {"range": "173", "text": "174"}, {"range": "175", "text": "176"}, {"range": "177", "text": "178"}, "prefer-template", "Unexpected string concatenation.", "BinaryExpression", {"range": "179", "text": "180"}, {"range": "181", "text": "182"}, {"range": "183", "text": "184"}, {"range": "185", "text": "186"}, "'updateStatus' is defined but never used.", "no-shadow", "'updateStatus' is already declared in the upper scope.", {"range": "187", "text": "188"}, [364, 1346], "(trx) => {\n                    return organizationDbConnection\n                        .select('*')\n                        .from('emp_job')\n                        .leftJoin('location', 'emp_job.Location_Id', 'location.Location_Id')\n                        .innerJoin('timezone', 'location.Zone_ID', 'timezone.Zone_ID')\n                        .where('Employee_Id', employeeId)\n                        .transacting(trx)\n                        .then(empTimeZone => {\n                            /** employee timestamp in YYYY-MM-DD HH:mm:ss */\n                            const timestamp = Moment()\n                                .tz(empTimeZone[0].TimeZone_Id)\n                                .format('YYYY-MM-DD HH:mm:ss');\n                            return timestamp;\n                        })\n                        .then(trx.commit) /** commit all the transactions */\n                        .catch(trx.rollback); /** rollback if any error occurs */\n                }", [1446, 1517], "(result) => {\n                    return result;\n                }", [1603, 1959], "(err) => {\n                    if (err.code === 'ECONNREFUSED') {\n                        console.log('missing database connection', err);\n                        return '';\n                    } else {\n                        console.log('error in database connection', err);\n                        return '';\n                    }\n                }", [1603, 1959], "function(err) {\n                    if (err.code === 'ECONNREFUSED') {\n                        console.log('missing database connection', err);\n                        return '';\n                    } \n                        console.log('error in database connection', err);\n                        return '';\n                    \n                }", [285, 924], "((resolve, reject) => {\n            const params = {\n                Bucket: bucket,\n                Key: path,\n                Body: data,\n                ContentType: contenttype\n            };\n            console.log('params',params);\n            S3.putObject(params, function(s3Err, s3Data) {\n                if (s3Err)\n                {\n                    console.log('Error in uploading file in s3',s3Err);\n                    resolve('');\n                }\n                else{\n                    console.log('File uploaded in bucket',s3Data);\n                    resolve(s3Data);\n                }\n            });\n        })", [561, 912], "(s3Err, s3Data) => {\n                if (s3Err)\n                {\n                    console.log('Error in uploading file in s3',s3Err);\n                    resolve('');\n                }\n                else{\n                    console.log('File uploaded in bucket',s3Data);\n                    resolve(s3Data);\n                }\n            }", [1029, 1494], "((resolve, reject) => {\n            const params = {\n            Bucket: bucket,\n            Key: path,\n        };\n        S3.deleteObject(params, function (err, data) {\n            if (data) {\n                console.log('File deleted successfully in s3 bucket',data);\n                resolve('');\n            }\n            else {\n                console.log('Error in deleting file from s3 ',err);\n                resolve('');\n            }\n        });\n    })", [1181, 1486], "(err, data) => {\n            if (data) {\n                console.log('File deleted successfully in s3 bucket',data);\n                resolve('');\n            }\n            else {\n                console.log('Error in deleting file from s3 ',err);\n                resolve('');\n            }\n        }", [1597, 2190], "((resolve, reject) => {\n            S3.getObject(\n                {\n                    Bucket: bucket,\n                    Key: key\n                },\n                function (err, data) {\n                    if (err) \n                    {\n                        console.log('Error in getting file from s3',err);\n                        reject('PBP0110');\n                    }\n                    else {\n                        console.log('File retrieved from s3 bucket',data);\n                        resolve (data);\n                    }\n                }\n            );\n        })", [1770, 2165], "(err, data) => {\n                    if (err) \n                    {\n                        console.log('Error in getting file from s3',err);\n                        reject('PBP0110');\n                    }\n                    else {\n                        console.log('File retrieved from s3 bucket',data);\n                        resolve (data);\n                    }\n                }", [455, 974], "`update bulk_processing set Job_Status=\"${status}\",Updated_On= \"${employeeTimeZone}\" where Request_Id=\"${requestId}\" and (select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id=\"${requestId}\" and ((PBP.Email_Status=\"Completed\" or PBP.Email_Status=\"Failure\") and (PBP.Generation_Status=\"Completed\" or PBP.Generation_Status=\"Failure\" or PBP.Generation_Status=\"Already Generated\")))=(select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id=\"${requestId}\")`", [1036, 1490], "`update bulk_processing set Job_Status=\"${status}\",Updated_On= \"${employeeTimeZone}\" where Request_Id=\"${requestId}\" and (select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id=\"${requestId}\" and ((PBP.Generation_Status=\"Completed\" or PBP.Generation_Status=\"Failure\" or PBP.Generation_Status=\"Already Generated\")))=(select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id=\"${requestId}\")`", [1771, 2095], "(updatejobStatus) =>\n                {\n                    console.log('updatejobStatus',updatejobStatus[0].affectedRows)\n                    if(updatejobStatus[0].affectedRows){\n                        return 'completed';}\n                    \n                        return '';\n                    \n                }", [2120, 2251], "(err) => {\n                    console.log('Err in updating job status',err);\n                    return '';\n                }", [1606, 1733], "(err) => {\n                    console.log('Err in updating status',err);\n                    return '';\n                }"]