// require jsdom
import jquery from 'jquery';
const jsdom = require('jsdom');
const { JSDOM } = jsdom;

// Function to bind the values in payslip template
export default async (payslipData,result,payslipType,employeeName,reportCreator,displayAddress) => {
    try{
        let myJSDom;
        // adding footer for payslip
        let fileData=payslipData+'<div id="viewFormMonthlyPayslip" style="text-align:center; font-size:10px; color:#000; margin-top:10px">."This is system generated report. No signature is required".</div>'
        myJSDom = new JSDOM(fileData);
        let $ = jquery(myJSDom.window);
    
        $('#formActionPayslipSubGrid').hide();

        // get currency based on payslip type
        let currency = (payslipType == 'Monthly' || payslipType == 'BiMonthly') ? result.Currency : result.Payslip['Currency'] ;

        
        // If Display_Payslip_Address is enabled need to show this payslip
        if (displayAddress == 1) {
            $("#payslipStreet1").text(result.Payslip['Street1']+',');
            $("#payslipStreet2").text(result.Payslip['Street2']+',');
            $("#payslipCity").text(result.Payslip['City']+',');
            $("#payslipState").text(result.Payslip['State']+',');
            $("#payslipCountry").text(result.Payslip['Country']+',');
            $("#payslipPinCode").text(result.Payslip['Pincode']);
        }
        else {
            $('#payslipTemplateAddress').html('');
            $("#payslipStreet1").text('');
            $("#payslipStreet2").text('');
            $("#payslipCity").text('');
            $("#payslipState").text('');
            $("#payslipCountry").text('');
            $("#payslipPinCode").text('');
        }
    
        $("#formHeader").text(result.Org_Name['Org_Name']);
    
        // Based on show report creator flag in org_details table
        if (reportCreator) {
            $('#viewFormMonthlyPayslip').append(`<div style="text-align:right; font-size:10px; color:#000; margin-top:10px">Report generated by ${result.Payslip['First_Name']} ${result.Payslip['Last_Name']} on ${result.Payslip['Generated_On']} </div>`);
        }
    
        $("#earnAmt").html(``);
        $("#deductAmt").html(``);
        $("#earnAmt").text("Amount" + ((currency) ? "(" + currency + ")" : ''));
        $("#deductAmt").text("Amount" + ((currency) ? "(" + currency + ")" : ''));
        $('#formActionPayslip').hide();
        $('#formGeneratePayslip, #formGeneratePayslipSubGrid').hide();
        $('#viewFormMonthlySalaryUser').show();
        $('#viewFormMonthlyPayslip,#formReport').show();
        
        // Function to get the personal details
        let personaldetails=fnUpdatePersonalDetailsInPayslip($,result,employeeName,payslipType)

        // function to update earnings, deduction, organization contribution, and form16 tables
        fngetTableContent($,$('#earningsTab'), $('#deductTab'), $('#exemptionsPanel'), $('#form16SummaryTab'), $('#sec10ExemptionsPanel'), $('#contributionTab1'), $('#contributionTab2'), result, false,payslipType);

        let newhtml = myJSDom.serialize();
        return newhtml;    
    } 
    catch(error)
    {
       console.log('Error in binding values in template',error);
        return '';
    }
}


//Function to bind the personal details
function fnUpdatePersonalDetailsInPayslip($,result,employeeName,payslipType){
    // function to get leave details
    let levDetails = fngetLeaveDetails($,result.PaidLeave[2],result.UnpaidLeave[2]);
    let worked_count = (payslipType.toLowerCase() == "monthly" || payslipType.toLowerCase() == "bimonthly") ? result.WorkedDays : result.actualHours['daysWorked'] ;
    let paidleave = ((result.PaidLeave[1] != null) ? result.PaidLeave[1] : 0);
    let pfPolicy = ((result.Payslip['Pf_PolicyNo'] != null) ? result.Payslip['Pf_PolicyNo'] : '-');
    let unPaidleave = ((result.UnpaidLeave[1] != null) ? result.UnpaidLeave[1] : 0);
    let onDuty = ((result.OnDuty[1] != null) ? result.OnDuty[1] : 0);

    if(result.previousPaycyleDate && result.previousPaycyleDate['Consider_Cutoff_Days_For_Attendance_And_Timeoff'] == 'yes' && result.previousPaycyleDate['Cutoff_Day'] > 0){
        var prevUnPaidleave = (( result.PrevMonthUnpaidLeave.length > 0 && result.PrevMonthUnpaidLeave[1] != null) ? result.PrevMonthUnpaidLeave[1] : 0);
        $("#viewMonthlyPrevMonthUnpaidLeave").text(prevUnPaidleave);
        let previousMonthDateRange = "("+result.previousPaycyleDate['Salary_Date']+" To "+result.previousPaycyleDate['Last_SalaryDate']+")";
        let currentMonthDateRange = "("+result.currentPaycyleDate['Salary_Date']+" To "+result.currentPaycyleDate['Last_SalaryDate']+")";
        $('#viewMonthlyPrevMonthUnpaidLeaveLabel').text('Unpaid Leave '+previousMonthDateRange);
        $('#viewMonthlyUnpaidLeaveLabel').text('Unpaid Leave '+currentMonthDateRange);
    } else {
        $("#viewMonthlyPrevMonthUnpaidLeaveElement").hide();
    }

    $("#payMonth").text("Payslip for the Month of " + result.Payslip_Month);
    $("#viewMonthlyEmployeeId").text(result.Payslip['User_Defined_EmpId']);
    $("#viewMonthlyEmployeeName").text(employeeName);
    $("#viewMonthlyDesignation").text(result.Payslip['Designation_Name']);
    $("#viewMonthlyDateOfJoin").text(result.Payslip['Date_Of_Join']);
    $("#viewMonthlyDaysWorked").text(worked_count);
    $("#viewMonthlyPaidDays").text(result.PaidDays);
    $("#viewMonthlyPaidLeave").text(paidleave + levDetails[0]);
    $("#viewMonthlyUnpaidLeave").text(unPaidleave + levDetails[1]);
    $("#viewMonthlyOnDuty").text(onDuty);
    $("#viewMonthlyDepartment").text(result.Payslip['Department_Name']);
    $("#viewMonthlyPFAccountNumber").text(pfPolicy);
    $("#viewMonthlyESI").text((result.Payslip['Policy_No']) ? (result.Payslip['Policy_No']) : "-");
    $("#viewMonthlyPANNo").text(fnCheckNull(result.Emp_Pan));
    $("#viewMonthlUAN").text(fnCheckNull(result.Payslip['UAN']));
    $("#viewMonthlyBankName").text(fnCheckNull(result.Payslip['Bank_Name']));
    $("#viewMonthlyAadhaarNo").text(fnCheckNull(result.Payslip['Aadhaar_Card_Number']));
    $("#viewMonthlyBankAccountNo").text(fnCheckNull(result.Payslip['Bank_Account_Number']));
    $("#viewMonthlyDateOfRelieving").text(fnCheckNull(result.Payslip['Emp_Relieving_Date']));
    if(payslipType == "Hourly"){
        $("#viewHoursWorked").text(result.WorkedHours);
        $("#viewDayWages").text(result.actualHours['dayWage']);
        $("#viewOTHours").text(fnCheckNull(result.OverTimeHours));
        $("#viewHoliday").text(fnCheckNull(result.Holiday));
    }
}

// function to get leave details
function fngetLeaveDetails($,paidLev, unPaidLev){
    let pLeave = paidLev;
    let uPLeave = unPaidLev;
    let leav, upleav,leavePaid,leaveUnpaid;
    let lvarr = '';
    let ulvarr = '';
    let lName;
    $('#viewMonthlyPaidLeaveDetails').html('');
    $('#viewMonthlyUnpaidLeaveDetails').html('');
    let pLeaveLength=pLeave.length;
        if (pLeaveLength > 0) {
        leavePaid = '(';
        for (let x = 0; x < pLeaveLength; x++) {
            leav = pLeave[x][0].split(" ");
            lName = '';
            if (leav.length > 1) {
                for (let pl in leav) {
                    lName += leav[pl].substring(0, 1);
                }
            }
            else {
                lName += pLeave[x][0].substring(0, 2);
            }
            lName = lName.toUpperCase();
            leavePaid += lName + ' - ' + pLeave[x][1];
            lvarr += lName + ' - ' + pLeave[x][0];

            if (x != pLeave.length - 1) {
                lvarr += ', ';
                leavePaid += ', ';
            }
            $('#viewMonthlyPaidLeaveDetails').append('<tr class="child"><td class="col-md-10 text-left" id="pltype" style="height:40px;word-break: break-word;">' + pLeave[x][0] +'</td><td class="text-right" id="plbalance" style="height:40px">' + pLeave[x][1] + '</td></tr>');
        }
        leavePaid += ')';
    }
    else {
        leavePaid = '';
        $('#viewMonthlyPaidLeaveDetails').append('<tr class="child"><td class="col-md-10 text-left" id="pltype" style="height:40px;word-break: break-word;">-</td><td class="text-center" id="plbalance" style="height:40px">-</td></tr>');
    }

    let uPLeaveLength=uPLeave.length;
    if (uPLeaveLength > 0) {
        leaveUnpaid = '(';
        for (let y = 0; y < uPLeaveLength; y++) {
            upleav = uPLeave[y][0].split(' ');
            let ulName = '';
            if (upleav.length > 1) {
                for (let ul in upleav) {
                    ulName += upleav[ul].substring(0, 1);
                }
            }
            else {
                ulName += uPLeave[y][0].substring(0, 2);
            }

            ulName = ulName.toUpperCase();
            leaveUnpaid += ulName + ' - ' + uPLeave[y][1];
            ulvarr += (y === 0 && lvarr != '') ? (', ' + ulName + ' - ' + uPLeave[y][0]) : (ulName + ' - ' + uPLeave[y][0]);

            if (y != uPLeaveLength - 1) {
                leaveUnpaid += ', ';
                ulvarr += ', ';
            }
            $('#viewMonthlyUnpaidLeaveDetails').append('<tr class="child"><td class="col-md-10 text-left" id="upltype" style="height:40px;word-break: break-word;">' + uPLeave[y][0] +'</td><td class="text-right" id="uplbalance" style="height:40px">' + uPLeave[y][1] + '</td></tr>');
        }
        leaveUnpaid += ')';
    }
    else {
        leaveUnpaid = '';
        $('#viewMonthlyUnpaidLeaveDetails').append('<tr class="child"><td class="col-md-10 text-left" id="upltype" style="height:40px;word-break: break-word;">-</td><td class="text-center" id="uplbalance" style="height:40px">-</td></tr>');
    }
    let leaves = lvarr.concat(ulvarr);

    if (leaves != '') {
        $('#notes, #notesHourly').html('<b>Note :</b> ' + leaves);
        $('#notes, #notesHourly').attr('style', 'display:""');
    }
    else
        $('#notes, #notesHourly').attr('style', 'display:none');

    return [leavePaid,leaveUnpaid];
}

/* get table content */
function fngetTableContent($,earnVar, deductVar, exempVar, form16Var, sec10Var, contribution1Var, contribution2Var, result, templateVariable,payslipType) {
    function fngetOperation(val, var_type) {
        if (!templateVariable) {
            var_type.append(val)
        }
        else {
            if (var_type == earnVar) {
                earnVar += val;
            }
            else if (var_type == deductVar) {
                deductVar += val;
            }
            else if (var_type == exempVar) {
                exempVar += val;
            }
            else if (var_type == form16Var) {
                form16Var += val;
            }
            else {
                sec10Var += val;
            }
        }
    }
    let winSize;
    let maxVal = Math.max((result.Deduction).length, (result.Incentive).length);
    let totalIncentive = result.totalEarnings;
    let totalDeduction = result.totalDeductions;
    let height;
    let netPayAmt, currency, isPfEmployee, isIncuranceEmployee, isEtfEmployee;
    var isMultiComponentExist = false;
    
    if (!templateVariable) {
        earnVar.html('');
        deductVar.html('');
    }
    for (let i = 0; i < maxVal; i++) {
        if (result.Incentive[i]['Incentive_Name']) {
            if (result.Incentive[i]['Incentive_Name'] != undefined) {
                // totalIncentive = parseFloat(totalIncentive) + parseFloat(result.Incentive[i]['Incentive_Amount']);

                if (result.Incentive[i]['Description'] && result.Incentive[i]['Description'] !== "Multi_Components" && (result.Incentive[i]['Incentive_Name'] != 'Provident Fund' && result.Incentive[i]['Incentive_Name'] != 'Insurance' && result.Incentive[i]['Incentive_Name'] != 'Advance Salary')) {
                    $('#earnings').append();
                    fngetOperation('<tr class="child"><td class="col-md-10 text-left" id="incent" style="height:40px;word-break: break-word;">' +/*result.Incentive[i]['Incentive_Name']+' - '+*/result.Incentive[i]['Description'] + '</td><td class="text-right" id="incentAmt" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                }
                else {
                    if (result.Incentive[i]['Incentive_Name'] == 'Provident Fund') {
                        fngetOperation('<tr id="incent" class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"> Allowance - Other</td><td class="text-right" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                    }
                    else if (result.Incentive[i]['Incentive_Name'] == 'Insurance') {
                        fngetOperation('<tr id="incent" class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Allowance - Insurance</td><td class="text-right" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                    }
                    else {
                        if (result.Incentive[i]['Description'] === "Multi_Components") {
                            let earnHtmlContent = earnVar.html();
                            let earningHeading = result.Incentive[i]['Incentive_Name'] == "Basic Pay" ? "Pay Scale" : result.Incentive[i]['Incentive_Name'];
                            
                            if(!earnHtmlContent.includes("Basic Pay")) {
                                isMultiComponentExist = true;
                                fngetOperation('<tr><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Basic Pay</td><td class="text-right" style="height:40px"></td></tr>', earnVar);
                            }
                            fngetOperation('<tr id="incent" class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;padding-left: 20px;">' + earningHeading + '</td><td class="text-right" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                        } else {
                            fngetOperation('<tr id="incent" class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + result.Incentive[i]['Incentive_Name'] + '</td><td class="text-right" style="height:40px">' + result.Incentive[i]['Incentive_Amount'] + '</td></tr>', earnVar);
                        }
                    }
                }
            }
            else {
                
                if (winSize > 991)
                    fngetOperation('<tr class="child"><td style="height:40px"></td><td style="height:' + height + '"></td></tr>', earnVar);
                else
                    $('#earndiv').attr({ 'style': 'display:none' });
            }
        }
        else {
            if (winSize < 991)
                $('#earndiv').attr({ 'style': 'display:none' });
            else
                fngetOperation('<tr class="child"><td style="height:40px"></td><td style="height:' + height + '"></td></tr>', earnVar);
        }
    }

    if(isMultiComponentExist) {
        maxVal += 1;
    }

    for (let i = 0; i < maxVal; i++) {
        if (result.Deduction[i]) {
            if (result.Deduction[i]['Deduction_Name'] != undefined) {
                // totalDeduction = parseFloat(totalDeduction) + parseFloat(result.Deduction[i]['Deduction_Amount']);

                if (result.Deduction[i]['Description']) {
                    fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">'/*+result.Deduction[i]['Deduction_Name']+' - '*/ + result.Deduction[i]['Description'] + '</td><td class="text-right" style="height:40px">' + result.Deduction[i]['Deduction_Amount'] + '</td></tr>', deductVar);
                }
                else {
                    fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + result.Deduction[i]['Deduction_Name'] + '</td><td class="text-right" style="height:40px">' + result.Deduction[i]['Deduction_Amount'] + '</td></tr>', deductVar);
                }
            }
            else {
                fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px;word-break: break-word;"></td><td style="height:40px"></td></tr>', deductVar);
            }
        }
        else {
            fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td style="height:40px"></td></tr>', deductVar);
        }
    }

    if(payslipType == 'Monthly' || payslipType == 'BiMonthly'){
        netPayAmt   =  result.Payslip['Incentive_Amount'] ;
        currency    =  result.Currency ;
        isPfEmployee = result.salaryDetails.Is_PfEmployee;
        isIncuranceEmployee = result.salaryDetails.Is_InsuranceEmployee;
        isEtfEmployee = result.salaryDetails.Is_ETFEmployee
    }else{
        netPayAmt   =  result.Payslip['Total_Salary'];
        currency    =  result.Payslip['Currency'];
        isPfEmployee = result.salaryDetails.Is_Pf;
        isIncuranceEmployee = result.salaryDetails.Is_Insurance;
        isEtfEmployee = 0;
    }





    if ($("#earningsTabFooter").length && $("#earningsTabFooter tr").length > 0) {
        $('#totalEarningsAmount').text(result.totalEarnings);
        $('#totalDeductionsAmount').text(result.totalDeductions);
        $('#totalOutstandingAmount').text(result.Payslip['Outstanding_Amt']);
        $('#totalNetPay').text(netPayAmt);

        if (typeof result.IntermediatePaymentAmt === 'string') {
            // Apply the replace function
            intermediatePaymentAmt = parseFloat(result.IntermediatePaymentAmt.replace(/[^0-9.]/g, ''));
        }
        else
        {
            intermediatePaymentAmt = result.IntermediatePaymentAmt;
        }

        if (intermediatePaymentAmt > 0)
        {
            $('.totalIntermediatePaymentPanel').show();
            $('#totalIntermediatePayment').text(intermediatePaymentAmt);
        }
        else
        {
            $('.totalIntermediatePaymentPanel').hide();
        }
        fngetOperation($("#earningsTabFooter tr"),earnVar);
        fngetOperation($("#deductionsTabFooter tr"),deductVar);

    } else {
        // total earnings
        fngetOperation('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Total Earnings</td><td class="text-right" style="height:40px">' + result.totalEarnings + '</td></tr>', earnVar);

        // total deductions
        fngetOperation('<tr class="child" id="totEarn" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Total Deductions</td><td class="text-right" style="height:40px">' + (result.totalDeductions) + '</td></tr>',deductVar);

        if (typeof result.IntermediatePaymentAmt === 'string') {
            // Apply the replace function
            intermediatePaymentAmt = parseFloat(result.IntermediatePaymentAmt.replace(/[^0-9.]/g, ''));
        }
        else
        {
            intermediatePaymentAmt = result.IntermediatePaymentAmt;
        }

        // intermediate payment is available, then add empty cell in deduction
        if (intermediatePaymentAmt > 0){
            fngetOperation('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Total Intermediate Payment</td><td class="text-right" style="height:40px">' + (result.IntermediatePaymentAmt) + '</td></tr>', earnVar);
            fngetOperation('<tr class="child" id="netPay" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td class="text-right" style="height:40px"></td></tr>',deductVar);
        }

        // outstanding amount
        fngetOperation('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Outstanding Amount</td><td class="text-right" style="height:40px">' + result.Payslip['Outstanding_Amt'] + '</td></tr>', earnVar);

        // net pay
        fngetOperation('<tr class="child" id="netPay" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Netpay</td><td class="text-right" style="height:40px">' + netPayAmt + '</td></tr>',deductVar);
    }








    $("#totEarn, #netPay, #intermediatePayment, .viewInsuranceDetails, .viewFHInsuranceDetails").remove();

    // total earnings
    fngetOperation('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Total Earnings</td><td class="text-right" style="height:40px">' + totalIncentive + '</td></tr>', earnVar);

    // total deductions
    fngetOperation('<tr class="child" id="totEarn" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Total Deductions</td><td class="text-right" style="height:40px">' + totalDeduction + '</td></tr>',deductVar);

    // intermediate payment is available, then add empty cell in deduction
    let intermediatePaymentAmt = result.IntermediatePaymentAmt ? parseFloat(result.IntermediatePaymentAmt.replace(/[^0-9.]/g, '')) : 0;
    if (intermediatePaymentAmt > 0){
        fngetOperation('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Total Intermediate Payment</td><td class="text-right" style="height:40px">' + (result.IntermediatePaymentAmt) + '</td></tr>', earnVar);
        fngetOperation('<tr class="child" id="netPay" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td class="text-right" style="height:40px"></td></tr>',deductVar);
    }

    // outstanding amount
    fngetOperation('<tr class="child" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Outstanding Amount</td><td class="text-right" style="height:40px">' + result.Payslip['Outstanding_Amt'] + '</td></tr>', earnVar);

    // net pay
    fngetOperation('<tr class="child" id="netPay" style="font-weight:bold;border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Netpay</td><td class="text-right" style="height:40px">' + netPayAmt + '</td></tr>',deductVar);

    if(netPayAmt){
        $('#netpayInWord').html(currency ? `<b>: ${currency}</b>`+'  '+ number_to_words(netPayAmt) + ' Only' : ': '+number_to_words(netPayAmt) + ' Only');
        $('#netpayInWord').attr('style', 'display:inline-block;');
    }

    let orgLwfAmount = result.orgLwfAmount ? parseFloat(result.orgLwfAmount.replace(/[^0-9.]/g, '')) : 0;

    if (isPfEmployee == 1 || isIncuranceEmployee == 1 || isEtfEmployee == 1 || orgLwfAmount > 0 ||  templateVariable ) {
        $('#empContribution').attr('style', 'display:block;');
        let contributionDetails = fngetContributionDetails(result.orgPfAmt,result.orgPfAmtHeader,result.adminCharge,result.edliCharge,result.etfAmt,result.orgLwfAmount,
            result.orgLWFAmtHeader,result.InsuranceDetails,result.FixedHealthInsurance);
        $('#contributionTab1,#contributionTab2').html('');
        let contributeLength=contributionDetails.length;
        let i;
        for(i=0; i< contributeLength; i++){
            if(i%2 == 0){
                if(templateVariable){
                    contribution1Var +=  contributionDetails[i];
                }
                else{
                    contribution1Var.append(contributionDetails[i]);
                }
            }else{
                if(templateVariable){
                    contribution2Var +=  contributionDetails[i];
                }
                else{
                    contribution2Var.append(contributionDetails[i]);
                }
            }
        }
        if(contributeLength%2 != 0){
            if(templateVariable){
                contribution2Var +=  '<tr class="child hidden-xs hidden-sm" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td class="text-right" style="height:40px"></td></tr>';
            }
            else{
                contribution2Var.append('<tr class="child hidden-xs hidden-sm" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;"></td><td class="text-right" style="height:40px"></td></tr>');
            }
        }
    }
    else{
        $('#emptyContributionDiv').attr('style', 'height: 0px;');
        $('#empContribution').attr('style', 'display:none');
    }

/** Form16 Summary **/
if (result.Form16Exists && result.Form16Exists == 1 && (payslipType == "Monthly" || payslipType == "BiMonthly")) {
    if (!templateVariable) {
        form16Var.html('');
        sec10Var.html('');
        exempVar.html('');
    }
    let currencySymbol = ((currency) ? "(" + currency + ")" : '');
    $("#exemptionsAmt").html(``);
    $("#sectionsAmt").html(``);
    $("#form16SummaryAmt").html(``);
    $("#exemptionsAmt").text("Amount" + currencySymbol);
    $("#sectionsAmt").text("Amount" + currencySymbol);    
    $("#form16SummaryAmt").text("Amount" + currencySymbol);

    /** Perks/Other Income/Exemptions/Rebates **/
    let form16TaxDetails = result.Form16['form16TaxDetails'];
    let form16TaxLength=form16TaxDetails.length;
    if (form16TaxLength > 0) {
        for (let i = 0; i < form16TaxLength; i++) {
            if ((form16TaxDetails[i]['ExemptionName'] !== '')) {
                fngetOperation('<tr class="child" id="E' + form16TaxDetails[i]['ExemptionName'] + '"><td class="col-md-10 text-left" style="height:40px">' + form16TaxDetails[i]['ExemptionName'] + '</td><td class="text-right" style="height:40px">' + form16TaxDetails[i]['ExemptionAmount'] + '</td></tr>', exempVar);
            }
            else {
                fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>', exempVar);
            }
        }
    }
    else {
        fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>', exempVar);
    }

    /** Sec 10 Exemptions **/

    let sec10Exemption = result.Form16['Sec10Exemption'];
    let sec10Length=sec10Exemption.length;
    if (sec10Length > 0) {
        for (let i = 0; i < sec10Length; i++) {
            if ((sec10Exemption[i]['Investment_Category'] != undefined && sec10Exemption[i]['Investment_Category'] != '')) {
                fngetOperation('<tr class="child" id="S' + sec10Exemption[i]['Investment_Category'] + '"><td class="col-md-10 text-left" style="height:40px">' + sec10Exemption[i]['Investment_Category'] + '</td><td class="text-right" style="height:40px">' + sec10Exemption[i]['ExemptAmt'] + '</td></tr>', sec10Var);
            }
            else {
                fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>', sec10Var);
            }
        }
    }
    else {
        fngetOperation('<tr class="child"><td class="col-md-10 text-left"style="height:40px"></td><td style="height:40px"></td></tr>', sec10Var);
    }

    /** Form16 Summary **/

    let form16Summary = result.Form16['form16Summary'];
    let form16Length=form16Summary.length;
    if (form16Length > 0) {
        for (let fs = 0; fs < form16Length; fs++) {


            if (form16Summary[fs]['SummaryName'] != '') {
                fngetOperation('<tr class="child" id="' + form16Summary[fs]['SummaryName'] + '"><td class="col-md-10 text-left" style="height:40px">' + form16Summary[fs]['SummaryName'] + '</td><td class="text-right" style="height:40px">' + form16Summary[fs]['SummaryAmount'] + '</td></tr>', form16Var);
            }
            else {
                fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px"></td><td class="text-right" style="height:40px"></td></tr>', form16Var);
            }
        }
    }
    else {
        fngetOperation('<tr class="child"><td class="col-md-10 text-left" style="height:40px"></td><td class="text-right" style="height:40px"></td></tr>', form16Var);
    }
}
else {
    $('#empForm16Table').attr('style', 'display:none');
    $('#emptyForm16Div').removeAttr('style');
    $('#form16SummaryTab tbody').html('');
}
return [earnVar, deductVar, exempVar, form16Var, sec10Var, contribution1Var, contribution2Var];

}

//  function to convert number to words
function number_to_words(amount){
    if(amount != 0){
     let words = new Array();
     words[0] = '';
     words[1] = 'One';
     words[2] = 'Two';
     words[3] = 'Three';
     words[4] = 'Four';
     words[5] = 'Five';
     words[6] = 'Six';
     words[7] = 'Seven';
     words[8] = 'Eight';
     words[9] = 'Nine';
     words[10] = 'Ten';
     words[11] = 'Eleven';
     words[12] = 'Twelve';
     words[13] = 'Thirteen';
     words[14] = 'Fourteen';
     words[15] = 'Fifteen';
     words[16] = 'Sixteen';
     words[17] = 'Seventeen';
     words[18] = 'Eighteen';
     words[19] = 'Nineteen';
     words[20] = 'Twenty';
     words[30] = 'Thirty';
     words[40] = 'Forty';
     words[50] = 'Fifty';
     words[60] = 'Sixty';
     words[70] = 'Seventy';
     words[80] = 'Eighty';
     words[90] = 'Ninety';
     amount = amount.toString();
     let atemp = amount.split(".");
     let number = atemp[0].split(",").join("");
     let n_length = number.length;
     let words_string = "";
     if (n_length <= 9) {
         let n_array = new Array(0, 0, 0, 0, 0, 0, 0, 0, 0);
         for(let i = n_length-1,j = 8; i >= 0; i--,j--)
         {
             n_array[j] = +number[i];
         }
         for (let i = 0, j = 1; i < 9; i++, j++) {
             if (i == 0 || i == 2 || i == 4 || i == 7) {
                 if (n_array[i] == 1) {
                     n_array[j] = 10 + parseInt(n_array[j]);
                     n_array[i] = 0;
                 }
             }
         }
 
         let value = "";
         for (let i = 0; i < 9; i++) {
             if (i == 0 || i == 2 || i == 4 || i == 7) {
                 value = n_array[i] * 10;
             } else {
                 value = n_array[i];
             }
             if (value != 0) {
                 words_string += words[value] + " ";
             }
             if ((i == 1 && value != 0) || (i == 0 && value != 0 && n_array[i + 1] == 0)) {
                 words_string += "Crore ";
             }
             if ((i == 3 && value != 0) || (i == 2 && value != 0 && n_array[i + 1] == 0)) {
                 words_string += "Lakh ";
             }
             if ((i == 5 && value != 0) || (i == 4 && value != 0 && n_array[i + 1] == 0)) {
                 words_string += "Thousand ";
             }
             if (i == 6 && value != 0 && (n_array[i + 1] != 0 && n_array[i + 2] != 0)) {
                 words_string += "Hundred and ";
             } else if (i == 6 && value != 0) {
                 words_string += 'Hundred ';
             }
         }
         words_string = words_string.split(" ").join(" ");
     }
     return words_string;
    }else{
        return 'Zero';
    }
 }
 
/**
 *  Check if the value is null or empty set '-'
*/
function fnCheckNull (value) {
    return (value == null || value == '') ? '-' : value;
}

// function to get contribution details
function fngetContributionDetails(orgPfAmt,orgPfAmtHeader,adminCharge,edliCharge,etfAmt,orgLwfAmount,orgLWFAmtHeader,InsuranceDetails,FixedHealthInsurance){
    let contributionDetails=[];
    let floatOrgPfAmt     = parseFloat(orgPfAmt.replace(/[^0-9.]/g, ''));
    let floatAdminCharge  = parseFloat(adminCharge.replace(/[^0-9.]/g, ''));
    let floatEdliCharge   = parseFloat(edliCharge.replace(/[^0-9.]/g, ''));
    let floatEtfAmt       = parseFloat(etfAmt.replace(/[^0-9.]/g, ''));
    let floatOrgLwfAmount = parseFloat(orgLwfAmount.replace(/[^0-9.]/g, ''));

    // check pf is avilable
    if (floatOrgPfAmt > 0) {
        contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + orgPfAmtHeader + '</td><td class="text-right" style="height:40px">' + orgPfAmt + '</td></tr>');
        // check adminCharge is avilable
        if (floatAdminCharge > 0){
            contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + 'Admin Charge' + '</td><td class="text-right" style="height:40px">' + adminCharge + '</td></tr>');
        }
        // check edliCharge is avilable
        if (floatEdliCharge > 0){
            contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + 'EDLI Charge' + '</td><td class="text-right" style="height:40px">' + edliCharge + '</td></tr>');
        }
    }
    // check etfAmt is avilable
    if (floatEtfAmt > 0){
        contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">Org ETF Amount</td><td class="text-right" style="height:40px">' + etfAmt + '</td></tr>');
    }
    // check orgLwfAmount is avilable
    if (floatOrgLwfAmount > 0){
        contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + orgLWFAmtHeader + '</td><td class="text-right" style="height:40px">' + orgLwfAmount + '</td></tr>');
    }
    // check InsuranceDetails is avilable
    if (InsuranceDetails.length > 0) {
        for (let i = 0; i < InsuranceDetails.length; i++) {
            contributionDetails.push('<tr class="child" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + InsuranceDetails[i].InsuranceName + '</td><td class="text-right" style="height:40px">' + InsuranceDetails[i].Amount + '</td></tr>');
        }
    }
    // check FixedHealthInsurance is avilable
    if(FixedHealthInsurance.length > 0){
        let j;
        for (j = 0; j < FixedHealthInsurance.length; j++) {
            contributionDetails.push('<tr class="child viewFHInsuranceDetails" style="border-top:1px solid"><td class="col-md-10 text-left" style="height:40px;word-break: break-word;">' + FixedHealthInsurance[j].Insurance_Name + '</td><td class="text-right" style="height:40px">' + FixedHealthInsurance[j].Insurance_Amount + '</td></tr>');
        }
    }
    return contributionDetails;
}