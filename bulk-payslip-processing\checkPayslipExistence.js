'use strict'
// require the file to get the employee time zone
import  empTimeZoneDetails  from './employeetimezone';
// require file to update the job status
import  statusUpdate from './updateJobStatus';
// require file to make database connection
import  makeConnection from './getConnection';

// Function to check whether payslip is already generated or not. 
// And get the details for generating payslip
export const startFunction = async (event, context) => {

    console.log('event in check payslip existence function');
    let inputData;
    // Since this function will be invoked from enpoint as well as lambda. So it is handle like this
    if(event){
        if(event.input){
            inputData = event.input;
        }
        else{
            inputData = event;
        }
    }

    // variable declaration
    let payslipType;
    let salaryMonth;
    let payslipId;
    let employeeId;
    let requestId;
    let orgCode;
    let templateId;
    let Encrypted;
    let jobType;
    let loggedEmployeeId;

    try{
        // Input validation
        if((!inputData.requestId) || (!inputData.orgCode) || (!inputData.templateId))
        {
            const response = 
            {
                string: 'ErrorState',
                input:{'errorCode':'IVE0000','requestId':requestId,'orgCode':orgCode,'templateId':templateId},
                message: 'Error in payslip existence function'
            };
            return response;                
        }
        else{
            requestId=inputData.requestId;
            orgCode=inputData.orgCode;
            templateId=inputData.templateId;

            // get database connection
            const organizationDb = await makeConnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);
            if(organizationDb){
                // Retrieve the job_type and addedBy from bulk processing
                return(
                    organizationDb('bulk_processing')
                    .select('Job_Type','Added_By')
                    .where('Request_Id',requestId)
                    .andWhere('Job_Status','Inprogress')
                    .then(async(getrecord) =>
                        {
                            // Check whether data exists or not
                            if(getrecord.length>0)
                            {
                                jobType=getrecord[0].Job_Type;
                                loggedEmployeeId=getrecord[0].Added_By;
                                // Retrieve payslipId,payslipType from payslip_bulk_processing table
                                return(
                                    organizationDb('payslip_bulk_processing')
                                    .select('Payslip_Id','Payslip_Type')
                                    .where('Request_Id',requestId)
                                    .andWhere('Generation_Status','Open')
                                    .then(async(version) =>
                                        {
                                            if(version.length>0){
                                            payslipId=version[0].Payslip_Id;
                                            payslipType=version[0].Payslip_Type;     
                                            // formation of payslip table name based on type          

                                            //we need to do changes here =>bwd_salary_payslip
                                            const payslipTable = (payslipType==='Monthly') ? 'salary_payslip' :  (payslipType==='Hourly') ? 'hourlywages_payslip': 'bwd_salary_payslip';

                                            // Get the empId,S3_File_Encrypted and S3_filename from payslip table
                                            return(
                                            organizationDb(payslipTable)
                                            .select('Employee_Id','S3_FileName','Salary_Month','S3_File_Encrypted')
                                            .where('Payslip_Id',payslipId)
                                            .then(async(payslipData) =>
                                            {
                                                // check payslipdata exist or not
                                                if(payslipData.length>0){
                                                
                                                const payslipFileName=payslipData[0].S3_FileName;
                                                employeeId=payslipData[0].Employee_Id;
                                                salaryMonth=payslipData[0].Salary_Month;
                                                Encrypted=payslipData[0].S3_File_Encrypted;

                                                // Check whether job type is email
                                                if(jobType==='Email')
                                                {    
                                                    // get the emp mailId based on empId from emp_job table
                                                    return(
                                                        organizationDb('emp_job')
                                                        .select('Emp_Email')
                                                        .where('Employee_Id',employeeId)
                                                        .then(async(getemail) =>
                                                        {
                                                            if(getemail.length>0)
                                                            {
                                                                const emailId=getemail[0].Emp_Email;
                                                                // get the employeename
                                                                const getPersonalData=await getEmployeeName(organizationDb,employeeId);
                                                                // check whether errcode is returned or not
                                                                if(getPersonalData.errCode==='')
                                                                {
                                                                    // if email and filename exist them move to mail step
                                                                    if(emailId){
                                                                        // check filename exist or not
                                                                        if(payslipFileName)
                                                                        {
                                                                            // update the status in payslip bulk processing table.
                                                                            const updateGenerationStatus= await updateRequestStatus(loggedEmployeeId,organizationDb,payslipId,requestId);
                                                                            if(updateGenerationStatus==='success'){
                                                                                const response = 
                                                                                {
                                                                                    string:'Mail',
                                                                                    input:{'requestId':requestId,'payslipId':payslipId,'orgCode':orgCode,'emailId':emailId,'payslipFileName':payslipFileName,'templateId':templateId,'employeeName':getPersonalData.employeeName,'payslipMonth':salaryMonth,'payslipType':payslipType,'Encrypted':Encrypted,'addedBy':loggedEmployeeId},
                                                                                    message: 'Send email to that employee'
                                                                                };
                                                                                return response;  
                                                                            }
                                                                            else{
                                                                                throw updateGenerationStatus;
                                                                            }
                                                                        }
                                                                        else{
                                                                            console.log('EmailId exist for this employee,need to generate payslip');
                                                                            // Since file is not generated move to payslip formation
                                                                            const response = 
                                                                            {
                                                                                string:'NextStep',
                                                                                input:{'requestId':requestId,'payslipId':payslipId,'orgCode':orgCode,'templateId':templateId,'payslipType':payslipType,'employeeId':employeeId,'salaryMonth':salaryMonth,'employeeName':getPersonalData.employeeName,'addedBy':loggedEmployeeId},
                                                                                message: 'Move to payslip formation step'
                                                                            };
                                                                            return response;
                                                                        }
                                                                    }
                                                                    else{
                                                                        console.log('Email not exist for this employee')
                                                                        throw 'PBP0106'
                                                                    }
                                                                }
                                                                else{
                                                                    const response = 
                                                                    {
                                                                        string: 'ErrorState',
                                                                        input:{'errorCode':getPersonalData.errCode,'payslipId':payslipId,'requestId':requestId,'orgCode':orgCode,'templateId':templateId,'addedBy':loggedEmployeeId},
                                                                        message: 'Error in payslip existence function'
                                                                    };
                                                                    return response;     
                                                                }
                                                            }
                                                            else{
                                                                console.log('Email not exist for this employee');
                                                                throw 'PBP0106';
                                                            }
                                                        })
                                                    );
                                                }
                                                else{
                                                    // get the employeename
                                                    const getPersonalData= await getEmployeeName(organizationDb,employeeId);
                                                    // Check whether errocode exist or not
                                                    if(getPersonalData.errCode==='')
                                                    {
                                                        if(payslipFileName)
                                                        {
                                                            // update the status in payslip bulk processing table
                                                            const updateGenerationStatus= await updateRequestStatus(loggedEmployeeId,organizationDb,payslipId,requestId)
                                                            if(updateGenerationStatus==='success'){
                                                                //  if job type is generation
                                                                const response = 
                                                                {
                                                                    string: 'Step1',
                                                                    input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},
                                                                    message: 'Check for other payslip'
                                                                };
                                                                return response;
                                                            }
                                                            else{
                                                                throw updateGenerationStatus;
                                                            }
                                                        }
                                                        else{
                                                            console.log('Payslip pdf need to generate')
                                                            const response = 
                                                            {
                                                                string:'NextStep',
                                                                input:{'requestId':requestId,'payslipId':payslipId,'orgCode':orgCode,'templateId':templateId,'payslipType':payslipType,'employeeId':employeeId,'salaryMonth':salaryMonth,'employeeName':getPersonalData.employeeName,'addedBy':loggedEmployeeId},
                                                                message: 'Move to payslip formation step'
                                                            };
                                                            return response;
                                                        }
                                                    }
                                                    else{
                                                        const response = 
                                                        {
                                                            string: 'ErrorState',
                                                            input:{'errorCode':getPersonalData.errCode,'payslipId':payslipId,'requestId':requestId,'orgCode':orgCode,'templateId':templateId,'addedBy':loggedEmployeeId},
                                                            message: 'Error in payslip existence function'
                                                        };
                                                        return response;   
                                                    }

                                                }
                                                }
                                                else{
                                                    console.log('No data found in payslip table');
                                                    throw '_EC0001';
                                                }
                                            })
                                    );
                                        }
                                        else{
                                            console.log('No data found in payslip bulk processing table ');
                                            // calculate the timestamp based on logged empId and update the status
                                            const employeeTimeZone = await empTimeZoneDetails(loggedEmployeeId, organizationDb);
                                            // function to update the status in bulk processing table
                                            const statusupdate= await statusUpdate(organizationDb,'Completed',employeeTimeZone,requestId,jobType);
                                            if(statusupdate==='completed'){
                                                console.log('Job process is completed');
                                            }
                                            else
                                            {
                                                console.log('Job is Inprogress');
                                            }
                                            const response = 
                                            {
                                                string: 'Success',
                                                message: 'Step function completed'
                                            };
                                            return response;
                                        }
                                    })
                                );
                            }
                            else{
                                console.log('No data exist in bulk_processing table for processing.');
                                const response = 
                                {
                                    string: 'Success',
                                    message: 'Step function completed'
                                };
                                return response;
                            }
                        })
                        .catch((err) => 
                        {
                            console.log('Error in check payslip existence catch block',err);
                            let errCode= (err==='_EC0001' || 'PBP0106')? err : 'PBP0111';
                            const response = 
                            {
                                string: 'ErrorState',
                                input:{'errorCode':errCode,'payslipId':payslipId,'requestId':requestId,'orgCode':orgCode,'templateId':templateId,'addedBy':loggedEmployeeId},
                                message: 'Error in payslip existence function'
                            };
                            return response;                
                        })
                /** return the success result to user */
                .then(function (result) {
                    return result;
                })
                /** catch organizationDbConnection connectivity errors */
                .catch(function (err) {
                    console.log('error in catch block of check payslip existence', err);
                    const response = 
                    {
                        string: 'ErrorState',
                        input:{'errorCode':'PBP0111','payslipId':payslipId,'requestId':requestId,'orgCode':orgCode,'templateId':templateId,'addedBy':loggedEmployeeId},
                        message: 'Error in payslip existence function'
                    };
                    return response;                
                })
                // destroy the connection
                .finally(() => {
                    organizationDb.destroy();
                })
                );
            }
            else{
                console.log('Error in making database connection');
                const response = 
                {
                    string: 'ErrorState',
                    input:{'errorCode':'_DB0000','payslipId':payslipId,'requestId':requestId,'orgCode':orgCode,'templateId':templateId,'addedBy':loggedEmployeeId},
                    message: 'Error in payslip existence function'
                };
                return response;      
            }
        }
    }
    catch(error){
        console.log('Error in check payslip existence function()',error);
        const response = 
        {
          string: 'ErrorState',
          input:{'errorCode':'PBP0111','payslipId':payslipId,'requestId':requestId,'orgCode':orgCode,'templateId':templateId,'addedBy':loggedEmployeeId},
          message: 'Error in payslip existence function'
        };
        return response;                
    }
};

// get the employee name from emp personal table
async function getEmployeeName(organizationDb,employeeId){
    try{
        return(
            organizationDb.raw('select  CONCAT_WS (" ", Emp_First_Name,Emp_Middle_Name,Emp_Last_Name) as name from emp_personal_info where Employee_Id= "'+employeeId+'"')
            .then(async(personalData) =>
            {
                // check the length of data
                if(personalData[0].length>0){
                    const employeeName=personalData[0][0].name;
                    return ({'errCode':'','employeeName':employeeName});
                }
                else{
                    console.log('No personal details found');
                    return ({'errCode':'_EC0001'});
                }
            })
            .catch(function (err) {
                console.log('error in catch block in retrieve personal data and email', err);
                return ({'errCode':'PBP0002'});
                
            })
        );
    }
    catch(error){
        console.log('Error in getEmail function catch block',error);
        return ({'errCode':'PBP0002'});
    }
}

// update the generation status in payslip bulk processing table
async function updateRequestStatus(loggedEmployeeId,organizationDb,payslipId,requestId){
    try{
        // calculate employee timezone
        const  employeeTimeStamp = await empTimeZoneDetails(loggedEmployeeId, organizationDb);
        // Update the generation_status as Already generated in payslip_bulk_processing table
        return(
            organizationDb('payslip_bulk_processing')
            .update({ 'Generation_Status': 'Already Generated','Updated_On':employeeTimeStamp })
            .where('Payslip_Id',payslipId)
            .andWhere('Request_Id',requestId)
            .then((updateStatus) =>
            { 
                return 'success';
            })
            .catch(function (err) {
                console.log('error in catch block of updating status', err);
                return 'PBP0001';
                
            })
        );
    }
    catch(err){
        console.log('Error in updateGenerationStatus catch block',err);
        return 'PBP0001';
    }
}
