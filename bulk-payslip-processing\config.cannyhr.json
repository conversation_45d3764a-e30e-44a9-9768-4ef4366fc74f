{"securityGroupIds": ["sg-06e06ee52057fb09f"], "subnetIds": ["subnet-023ff1fb8431b273f", "subnet-09dd9cf2a9239643c"], "dbSecretName": "PROD/CANNY/PGACCESS", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::378423228887:role/lambdaFullAccess", "dbPrefix": "cannyhr_", "domainName": "cannyhr", "authorizerARN": "arn:aws:lambda:ap-south-1:378423228887:function:ATS-cannyhr-firebaseauthorizer", "fromAddress": "<EMAIL>", "sesTemplatesRegion": "us-west-2", "webAddress": ".com", "customDomainName": "api.cannyhr.com", "logoBucket": "s3.logos.cannyhr.com", "bucketName": "s3.taxdocs.cannyhr.com", "layers": "arn:aws:lambda:ap-south-1:378423228887:layer:dev-canva:1"}