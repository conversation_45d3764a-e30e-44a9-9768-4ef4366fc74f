{"securityGroupIds": ["sg-098a0324de5632c9e", "sg-03b2b753038f67293"], "subnetIds": ["subnet-020dff85cddc0752e", "subnet-0b1a6fbc2c7b782ad"], "dbSecretName": "hrapp-stage", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::692647644057:role/lambdaFullAccess", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:692647644057:function:ATS-dev-firebaseauthorizer", "customDomainName": "api.hrapp.co.in", "bucketName": "caprice-dev-stage", "sesTemplatesRegion": "us-east-1", "fromAddress": "<EMAIL>", "webAddress": ".co.in", "logoBucket": "s3.hrapp-dev-public-images", "layers": "arn:aws:lambda:ap-south-1:692647644057:layer:canvas-lib64:1", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:692647644057:function:BULKPDFGENERATION-dev"}