{"securityGroupIds": ["sg-0d3854ad69d6e7e09", "sg-0a9a621d864c23783"], "subnetIds": ["subnet-075680669427eff9d", "subnet-0e2510550b4a177a5"], "dbSecretName": "prod/hrapp/pgaccess", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::484056187456:role/LambdaMicroservice", "dbPrefix": "hrapp_", "domainName": "hrapp", "authorizerARN": "arn:aws:lambda:ap-south-1:484056187456:function:ATS-prod-firebaseauthorizer", "customDomainName": "api.hrapp.co", "bucketName": "s3.taxdocs.hrapp.co", "sesTemplatesRegion": "us-west-2", "fromAddress": "<EMAIL>", "webAddress": ".co", "logoBucket": "s3.logos.hrapp.co", "layers": "arn:aws:lambda:ap-south-1:484056187456:layer:dev-canva:1", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:484056187456:function:BULKPDFGENERATION-prod"}