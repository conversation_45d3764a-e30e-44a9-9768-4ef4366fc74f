{"securityGroupIds": ["sg-04bc41f567ae532b6"], "subnetIds": ["subnet-0cbbb2391d86f1d0e", "subnet-0120a712c5936a292"], "dbSecretName": "PROD/UPSHOT/PGACCESS", "region": "eu-west-2", "lambdaRole": "arn:aws:iam::327313496531:role/lambdaFullAccess", "dbPrefix": "upshothr_", "domainName": "upshothr", "authorizerARN": "arn:aws:lambda:eu-west-2:327313496531:function:ATS-upshothr-firebaseauthorizer", "customDomainName": "api.upshothr.uk", "bucketName": "s3.taxdocs.upshothr.uk", "sesTemplatesRegion": "eu-west-2", "fromAddress": "<EMAIL>", "webAddress": ".uk", "logoBucket": "s3.logos.upshothr.uk", "layers": "arn:aws:lambda:eu-west-2:327313496531:layer:dev-canva:1"}