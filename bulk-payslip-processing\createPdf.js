'use strict'
import fs from 'fs';
// require package
// require file to delete/upload/retrieve file in  S3
import crudS3Function from './s3CommonFunction';
// require the file to get the employee time zone
import empTimeZoneDetails from './employeetimezone';
// require file to make database connection
import  makeConnection from './getConnection';
// require file to update status
import changeStatus from './updateStatusAndUploadFile';

// Function to form payslip and convert to base64 data
export const pdfCreation = async (event, context) => {

    console.log('event for create pdf function');
    let inputData;

    // Get the html content of payslip
    let input=event.input;
    let inputDataPath=input.draftFileNamePath;

    let payslipId=input.payslipId;
    let requestId=input.requestId;
    let addedBy=input.addedBy;
    let employeeId=input.employeeId;
    let orgCode=input.orgCode;
    let templateId=input.templateId;
    let employeeName=input.employeeName;
    let payslipMonth=input.payslipMonth;
    let payslipType=input.payslipType;
    try{
        // get database connection
        let organizationDb = await makeConnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);
        if(organizationDb){
            // Retrieve the job_type from bulk processing
            return(
                organizationDb('bulk_processing')
                .select('Job_Type')
                .where('Request_Id',requestId)
                .then(async(getrecord) =>
                {
                    // Check whether data exists or not
                    if(getrecord.length>0)
                    {
                        let jobType=getrecord[0].Job_Type;
                        // check Payslip_Password_Protected flag in org details table
                        return(
                            organizationDb('org_details')
                            .select('Payslip_Password_Protected')
                            .then(async(getOrgDetails) =>
                            {
                                // Check whether data exists or not
                                if(getOrgDetails.length>0)
                                {
                                    let isEncrypted=getOrgDetails[0].Payslip_Password_Protected;
                                    // Get the payslip template data from s3 bucket
                                    let fileData= await crudS3Function.getS3file(inputDataPath,process.env.bucketName);
                                    let content=fileData.Body;
                                    // Convert buffer to string
                                    inputData=content.toString('utf8');

                                    // get payslip year
                                    let payslipYear=payslipMonth.split(',')[1];	

                                    // example 04,2019 - convert as 042019
                                    let definedPayslipMonth= payslipMonth.replace(/[,\s]/g, '');

                                    // Remove the draft file created in s3 bucket
                                    let removefile= await crudS3Function.deleteFile(inputDataPath,process.env.bucketName);
                                    
                                    // file will be created and deleted within with function
                                    let pathOfFileToEncrypt= '/tmp/'+orgCode+requestId+'Payslip'+payslipId+'.pdf';

                                    const AWS = require('aws-sdk');
                                    const lambda = new AWS.Lambda();
                                    
                                    const params = {
                                        FunctionName: "PDFCREATION-"+process.env.stageName+"-createPDF", // Replace with the name of the target Lambda function
                                        InvocationType: 'RequestResponse', // Use 'Event' for asynchronous invocation, 'RequestResponse' for synchronous
                                        Payload: JSON.stringify({ 'inputData': inputData, 'pathOfFileToEncrypt': pathOfFileToEncrypt }) // Payload to send to the target function
                                    };

                                    try {
                                        let response = await lambda.invoke(params).promise();
                                        
                                        if(response && response.Payload && JSON.parse(response.Payload) ) {
                                            content = JSON.parse(response.Payload);
                                        }
                                    } catch (error) {
                                        console.error('Invocation error:', error);
                                    }
                                    // Convert the buffer to base64 data
                                    let bufferData = Buffer.from(content, 'utf-8');
                                    
                                    let base64data=bufferData.toString('base64');
                                    
                                    // Check whether file need to encrypt or not
                                    if(!isEncrypted){
                                        try{
                                            console.log('Starting non-encrypted PDF process');
                                            console.log('Content type:', typeof content);
                                            console.log('Content length:', content ? content.length : 'undefined');
                                            
                                            return(
                                                // Get user defined empId and emailid from emp_job table
                                                organizationDb('emp_job')
                                                .select('User_Defined_EmpId','Emp_Email')
                                                .where('Employee_Id', employeeId)
                                                .then(async(getUserDefinedId) =>
                                                {
                                                    // Check whether data exists or not
                                                    if(getUserDefinedId.length>0)
                                                    {
                                                        // user defined empId
                                                        let userDefinedEmpId=getUserDefinedId[0].User_Defined_EmpId;
                                                        let emailId=getUserDefinedId[0].Emp_Email;

                                                        // Formation of payslipFileName example: Payslip_10_042019.pdf
                                                        let payslipFileName=process.env.payslipFileNamePrefix+userDefinedEmpId+'_'+definedPayslipMonth+'.pdf';
                                                        
                                                        // formation of s3 bucket path
                                                        let path=process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+payslipYear+'/'+ payslipType+'/'+payslipFileName;

                                                        if (content && content.type === 'Buffer' && content.data) {
                                                            content = Buffer.from(content.data);
                                                        }                                                      
                                                        // upload the generated payslip pdf in s3 bucket
                                                        let uploadFileData= await crudS3Function.uploadS3file(content,path,process.env.bucketName,'application/pdf');
                                                        // if file is uploaded in s3 update the status
                                                        if(uploadFileData)
                                                        {
                                                            // get employee timestamp based on location
                                                            let employeeTimeZone = await empTimeZoneDetails(addedBy, organizationDb)
                                                            // update status,file name and isEncrypted flag as 0 (Since in this fucntion encryption is not done) in database
                                                            let updateRecord= await changeStatus(organizationDb,payslipFileName,requestId,payslipId,payslipType,employeeTimeZone,0,employeeId);
                                                            if(updateRecord){
                                                                // delete the file in tmp folder
                                                                let removefile = await removeFile(pathOfFileToEncrypt);
                                                                // If the requested job type is email move to send mail step
                                                                if(jobType==='Email')
                                                                {
                                                                    // Since encrypted is passed as 0 since pdf is not encrypted
                                                                    const response = 
                                                                    {
                                                                        string: 'Mail',
                                                                        input:{'requestId':requestId,'payslipId':payslipId,'orgCode':orgCode,'addedBy':addedBy,'emailId':emailId,'payslipFileName':payslipFileName,'templateId':templateId,'employeeName':employeeName,'payslipMonth':payslipMonth,'payslipType':payslipType,'Encrypted':0},
                                                                        message: 'Pdf generated successfully'
                                                                    };
                                                                    return response;
                                                                }
                                                                // Download - will be handled in future
                                                                // if the job type is Generation then move to step1
                                                                else{
                                                                    const response = 
                                                                    {
                                                                        string: 'Step1',
                                                                        input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},
                                                                        message: 'Pdf generated successfully'
                                                                    };
                                                                    return response; 
                                                                }
                                                            }
                                                            else{
                                                                console.log('Error in updating status and filename in table');
                                                                // delete the file in tmp folder
                                                                let removefile = await removeFile(pathOfFileToEncrypt);
                                                                let response= await outputResponse('PBP0001',payslipId,requestId,addedBy,orgCode,templateId);
                                                                return response;
                                                            }                                                 
                                                        }
                                                        else{
                                                            console.log('Error in uploading pdf in s3 bucket');
                                                            // delete the file in tmp folder
                                                            let removefile = await removeFile(pathOfFileToEncrypt);
                                                            let response= await outputResponse('PBP0104',payslipId,requestId,addedBy,orgCode,templateId);
                                                            return response;
                                                        }
                                                    }
                                                    else{
                                                        console.log('User defined employeeid not exist for this employee');
                                                        // delete the file in tmp folder
                                                        let removefile = await removeFile(pathOfFileToEncrypt);
                                                        let response= await outputResponse('_EC0001',payslipId,requestId,addedBy,orgCode,templateId);
                                                        return response;
                                                    }
                                                })
                                                .catch(async function(err) {
                                                    console.log('Error in retrieve user defined employee id',err);
                                                    // delete the file in tmp folder
                                                    let removefile = await removeFile(pathOfFileToEncrypt);
                                                    let response= await outputResponse('PBP0002',payslipId,requestId,addedBy,orgCode,templateId);
                                                    return response;
                                                })
                                            );
                                        }
                                        catch(error){
                                            console.log('error in uploading file and status update catch block',error);
                                            // delete the file in tmp folder
                                            let removefile = await removeFile(pathOfFileToEncrypt);
                                            let response= await outputResponse('PBP0104',payslipId,requestId,addedBy,orgCode,templateId);
                                            return response;                                      
                                        }
                                    }
                                    else{
                                        // delete the file in tmp folder
                                        let removefile = await removeFile(pathOfFileToEncrypt);
                                        
                                        // upload the base64 data in s3 and pass the path to generatepdf function
                                        let draftFileName='Draft_'+orgCode+requestId+payslipId+'base64.txt';
                                        let path= process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+payslipYear+'/'+payslipType+'/'+draftFileName;
                                        
                                        let fileData= await crudS3Function.uploadS3file(base64data,path,process.env.bucketName,'application/txt')
                                        console.log('response after uploading draft file in s3',fileData)
                                        if(fileData){
                                            const response = 
                                            {
                                                string: 'GeneratePdf',
                                                input:{'payslipId':payslipId,'requestId':requestId,'employeeId':employeeId,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName,'draftFileName':path,'payslipMonth':payslipMonth,'payslipType':payslipType,'addedBy':addedBy},
                                                message: 'Payslip formation completed successfully'
                                            };
                                            return response; 
                                        }
                                        else{
                                            console.log('Error in uploading file in s3')
                                            let response= await outputResponse('PBP0104',payslipId,requestId,addedBy,orgCode,templateId);  
                                            return response;                                    
                                        }
                                    }
                                }
                                else{
                                    console.log('No records exist in organization details');
                                    let response= await outputResponse('_EC0001',payslipId,requestId,addedBy,orgCode,templateId);  
                                    return response;                                    
                                }
                            })
                        )
                    }
                    else{
                        console.log('No records found in bulk processing table');
                        let response= await outputResponse('_EC0001',payslipId,requestId,addedBy,orgCode,templateId);
                        return response;                                      
                    }
                })
                .catch(async function(err) {
                    console.log('Error in retrieve job type',err);
                    let response= await outputResponse('PBP0002',payslipId,requestId,addedBy,orgCode,templateId);     
                    return response;                                 
                })
                .finally(() => {
                    organizationDb.destroy();
            }))
    }
    else{
        console.log('Error in making database connection');
        let response= await outputResponse('_DB0000',payslipId,requestId,addedBy,orgCode,templateId);    
        return response;                                  
    } 
    }
    catch(err){
        console.log('Error in creating pdf catch block',err);
        // Remove the draft file created in s3 bucket
        let removefile= await crudS3Function.deleteFile(inputDataPath,process.env.bucketName);
        let response= await outputResponse('PBP0108',payslipId,requestId,addedBy,orgCode,templateId);  
        return response;                                    
    }
}

// Function to remove file in temp folder
async function removeFile(pathOfFileToEncrypt){
    return new Promise(function (resolve, reject) {

        // Function to delete the file created in temp folder
       fs.unlink(pathOfFileToEncrypt, (err) => {
            if (err) 
            {
                console.log('Error in deleting file in tmp folder',err);
                resolve('');
            }
            else{
                console.log('file deleted successfully');
                resolve('success');
            }
        });  
    })
}

// return output response
function outputResponse(errCode,payslipId,requestId,addedBy,orgCode,templateId){
    const response = 
    {
        string: 'ErrorState',
        input:{'errorCode':errCode,'payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId},
        message: 'Error occured in payslip creation function'
    };
    return response;
}
