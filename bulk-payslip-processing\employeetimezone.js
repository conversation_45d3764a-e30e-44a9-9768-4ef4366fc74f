// require moment package to format time in required format
import Moment from 'moment-timezone';

export default async (employeeId, organizationDbConnection) => {
        // Get current time based on employee time zone by using employeeId
        return (
            organizationDbConnection
                .transaction(function(trx) {
                    return organizationDbConnection
                        .select('*')
                        .from('emp_job')
                        .leftJoin('location', 'emp_job.Location_Id', 'location.Location_Id')
                        .innerJoin('timezone', 'location.Zone_ID', 'timezone.Zone_ID')
                        .where('Employee_Id', employeeId)
                        .transacting(trx)
                        .then(empTimeZone => {
                            /** employee timestamp in YYYY-MM-DD HH:mm:ss */
                            const timestamp = Moment()
                                .tz(empTimeZone[0].TimeZone_Id)
                                .format('YYYY-MM-DD HH:mm:ss');
                            return timestamp;
                        })
                        .then(trx.commit) /** commit all the transactions */
                        .catch(trx.rollback); /** rollback if any error occurs */
                })
                /** return the employee time zone to the calling function*/
                .then(function(result) {
                    return result;
                })
                /** check and return if any error occured */
                .catch(function(err) {
                    if (err.code === 'ECONNREFUSED') {
                        console.log('missing database connection', err);
                        return '';
                    } else {
                        console.log('error in database connection', err);
                        return '';
                    }
                })
        );
};