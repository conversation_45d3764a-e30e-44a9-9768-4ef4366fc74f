'use strict'
//require fs package
import fs from 'fs';
const { spawn } = require('child_process');
//require file to get the timezone
import empTimeZoneDetails from './employeetimezone';
//require file to make database connection
import makeConnection from './getConnection';
//require file to delete/retrieve file from S3
import crudS3Function from './s3CommonFunction';
//require file to update status
import changeStatus from './updateStatusAndUploadFile';

// update the lambda path
 if (process.env.LAMBDA_TASK_ROOT) {
    process.env['PATH'] = process.env['PATH'] + ':' + process.env['LAMBDA_TASK_ROOT'] + '/bin/';
    process.env['LD_LIBRARY_PATH'] = process.env['LD_LIBRARY_PATH']+ ':' +process.env['LAMBDA_TASK_ROOT'] +  '/sharedlib/';
 }

// Function to encrypt the pdf with password and upload it in s3 bucket
export const encrypting = async (event, context) => {

    let inputData=event.input;
    let draftFileName=inputData.draftFileName;
    let userPassword=inputData.userPassword;
    let ownerPassword=inputData.ownerPassword;
    let requestId=inputData.requestId;
    let payslipId=inputData.payslipId;
    let orgCode=inputData.orgCode;
    let addedBy=inputData.addedBy;
    let emailId=inputData.emailId;
    let payslipFileName=inputData.payslipFileName;
    let templateId=inputData.templateId;
    let employeeName=inputData.employeeName;
    let payslipMonth=inputData.payslipMonth;
    let payslipType=inputData.payslipType;
    let employeeId=inputData.employeeId;

    // file path for creating sample in temp folder
    let pathOfFileToEncrypt='/tmp/'+orgCode+requestId+payslipFileName;
    let pathOfEncryptedFile='/tmp/'+orgCode+requestId+'Encrypt'+payslipFileName;

    console.log('Inside createPdf function');
    try{
        return new Promise(async function (resolve, reject) {
        let payslipTemplateData;
        // Get the payslip base64 data from s3 bucket
        let fileData= await crudS3Function.getS3file(draftFileName,process.env.bucketName);
        let content=fileData.Body;
        // Convert buffer to string
        payslipTemplateData=content.toString('utf8');
        
        // Remove the draft file uploaded in s3 bucket
        let removefile= await crudS3Function.deleteFile(draftFileName,process.env.bucketName);
        
        // create the pdf in local path with base64 input
        fs.writeFile(pathOfFileToEncrypt, payslipTemplateData, {encoding: 'base64'},async function(err,data) {
            if(err)
            {
                console.log('Error in write file in temp folder',err);
                outputResponse('PBP0103',payslipId,orgCode,requestId,addedBy,templateId);
            }
            else{
                // Encrypt the pdf using qpdf
                const qpdf = spawn('qpdf',
                ['--encrypt', userPassword, ownerPassword, '256', '--modify=none', '--', pathOfFileToEncrypt, pathOfEncryptedFile]);        
                qpdf.stderr.on('data', data => {
                    console.log(`Error from qpdf: ${data}`);
                    outputResponse('PBP0103',payslipId,orgCode,requestId,addedBy,templateId);
                });
                qpdf.on('close', code => {
                    // If file created successfully 0 is returned
                    if(code===0){
                        console.log(`child process exited with code ${code}`);
                        // upload the pdf in s3 bucket
                        s3upload(pathOfEncryptedFile,payslipFileName,orgCode,payslipType,payslipMonth,async function(err,data){
                            if(err){
                                console.log('Error in upload pdf in s3',err);
                                // delete the files in temp folder
                               await deleteTempFile(pathOfFileToEncrypt,pathOfEncryptedFile);
                               outputResponse('PBP0104',payslipId,orgCode,requestId,addedBy,templateId);
                            }
                            else{
                                // Delete the files created in temp folder
                                let deletefile=await deleteTempFile(pathOfFileToEncrypt,pathOfEncryptedFile);
                                // After upload file in S3 update the filename in table
                                let updateName=await updateFileName(payslipFileName,requestId,payslipId,payslipType,orgCode,addedBy,employeeId);
                                if(updateName){
                                    // validate whether request came for Email request
                                    if(updateName==='Email')
                                    {
                                        // since this is encryption file so encrypted flag is send as 1
                                        const response = 
                                        {
                                            string: 'Mail',
                                            input:{'requestId':requestId,'payslipId':payslipId,'orgCode':orgCode,'addedBy':addedBy,'emailId':emailId,'payslipFileName':payslipFileName,'templateId':templateId,'employeeName':employeeName,'payslipMonth':payslipMonth,'payslipType':payslipType,'Encrypted':1},
                                            message: 'Pdf generated successfully'
                                        };
                                        resolve(response);
                                    }
                                    else{
                                        // for job_type generation move to step1
                                        const response = 
                                        {
                                            string: 'Step1',
                                            input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},
                                            message: 'Pdf generated successfully'
                                        };
                                        resolve(response);
                                    }
                                }
                                else
                                {
                                    console.log('error in updating filename in database');
                                    outputResponse('PBP0001',payslipId,orgCode,requestId,addedBy,templateId);
                                }
                            }
                            })
                        }
                        else{
                            console.log('Error in encrypting payslip');
                            outputResponse('PBP0103',payslipId,orgCode,requestId,addedBy,templateId);
                        }
                });
            }
        });
        
        // function to return output response
        function outputResponse(errCode,payslipId,orgCode,requestId,addedBy,templateId){
            const response = 
            {
                string: 'ErrorState',
                input:{'errorCode':errCode,'payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},
                message: 'Error occured in pdf encryption process'
            };
            resolve(response);
        }
    })
    }
    catch(err){
        console.log('Error in encrypt pdf catch block',err);
        let errCode='PBP0103';
        // if error in retrieve file then delete the file in s3
        if(err==='PBP0110')
        {
            errCode='PBP0110';
            // Remove the draft file uploaded in s3 bucket
            let removefile= crudS3Function.deleteFile(draftFileName,process.env.bucketName)
        }
        const response = 
        {
            string: 'ErrorState',
            input:{'errorCode':errCode,'payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},
            message: 'Error occured in pdf encryption process'
        };
        return response;
    }
}

// Function to upload the generated payslip pdf in s3 bucket
async function s3upload(pathOfEncryptedFile,payslipFilename,orgCode,payslipType,payslipMonth,fn){
    try{
        let fileName=pathOfEncryptedFile;
        // example: payslipmonth:'02,2019' get 2019
        let payslipYear=payslipMonth.split(',')[1];
        let fileStream = fs.createReadStream(fileName);
        // Read the encrypted pdf in tmp folder
        fs.readFile(fileName, (err, data) => {
            if (err) 
            {
                console.log('Error in read file function',err)
                throw err
            }
            else{
                let path=process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+payslipYear+'/'+ payslipType+'/'+payslipFilename;
                let uploadFileData= crudS3Function.uploadS3file(fileStream,path,process.env.bucketName,'application/pdf');
                if(uploadFileData)
                {
                    fn(null,uploadFileData);
                }
                else{
                    console.log('Error in uploading pdf');
                    fn('Error',null);
                }
            }
        });
    }
    catch(error){
        console.log('error in uploading file in s3',error);
        fn(error,null)
    }
}

//on uploading s3 delete file in tmp folder
async function deleteTempFile(pathOfFileToEncrypt,pathOfEncryptedFile){
    try{
        const files = [pathOfFileToEncrypt,pathOfEncryptedFile];
        files.forEach(function(filePath) {
            fs.access(filePath, error => {
                if (!error) {
                    fs.unlinkSync(filePath,function(error){
                        if(error){
                            console.log('error in deleting temp files function',error);  
                            return '';                   
                        }                                         
                    });
                } else {
                    console.log('error in accessing files');
                    return '';
                }
            });
        });
    }
    catch(err){
        console.log('error in deleting files',err);
        return '';
    }
}

// Function to update the Generation_Status as 'Completed' and filename
async function updateFileName(fileName,requestId,payslipId,payslipType,orgCode,addedBy,employeeId){
try{
    // make database connection
    const organizationDb = await makeConnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);
    if(organizationDb){
    
    // get the job_type from bulk processing table
    return(
        organizationDb('bulk_processing')
        .select('Job_Type')
        .where('Request_Id',requestId)
        .then(async(getJobtype) =>
        {
            // Check whether data exist or not
            if(getJobtype.length>0){
                let jobType=getJobtype[0].Job_Type;

                // get employee timestamp based on location
                let employeeTimeZone = await empTimeZoneDetails(addedBy, organizationDb);
                
                // update the generation status and filename. since it is encrypted function so update 1 
                let updateRecord= await changeStatus(organizationDb,fileName,requestId,payslipId,payslipType,employeeTimeZone,1,employeeId);
                if(updateRecord){
                    return jobType;
                }
                else{
                    return '';
                }
            }
            else{
                console.log('job type is not available for this request');
                return '';
            }
        })
        .catch(function(err) {
            console.log('Err in updating status',err);
            return '';
        })
        .finally(() => {
            organizationDb.destroy();
    })
    );
    }
    else{
        console.log('Error in database connection');
        return '';
    }
}
catch(error){
    console.log('Error in updateFileName catch block',error);
    organizationDb.destroy();
    return '';
}
}
