// Require aws-sdk
import AWS from 'aws-sdk';
import knex from 'knex';

export default async (dbSecretName,dbPrefix,orgCode,region) => {
    try{            
        // Get secrets from aws secrets manager
        const client = new AWS.SecretsManager({
            region: region
        });

       // Call function getSecretValue to get secretvalues
        const secret= await client.getSecretValue({ SecretId: dbSecretName }).promise();
        const SecretString=JSON.parse(secret.SecretString);
        const OrganizationDb= {
                client: 'mysql',
                connection: {
                    host: SecretString.hostname,
                    user: SecretString.username,
                    password: SecretString.password,
                    database: dbPrefix + orgCode,
                    charset: 'utf8'
                },
                pool: { min: 1, max: 10 },
                acquireConnectionTimeout: 10000
            };
        // require knex for database connection
        const organizationDb = knex(OrganizationDb);
        return organizationDb;
    }
    catch(error){
        console.log('Error in making database connection',error);
        return '';
    }
};