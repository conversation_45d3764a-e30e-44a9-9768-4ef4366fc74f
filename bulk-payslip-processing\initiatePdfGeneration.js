'use strict'
// require the file to get the employee time zone
import empTimeZoneDetails from './employeetimezone';
// require file to make database connection
import makeConnection from './getConnection';
// require moment package in order the format date in required format
import moment from 'moment';

// Function to generate pdf along with password protected
// Pdf filename: Payslip-EmployeeId- MMYYYY (payslip month)- Payslip_10_042019
// Password for user: First four characters of emp_name + DOB (DDMMYYYY) - abcd30011995
// Password for admin: Orgcode_organization+ org_start_year. - caprice2019
    export const generateEncryptedPdf = async (event, context) => {

    // variable initialization
    let inputData=event.input;
    let requestId=inputData.requestId;
    let employeeId=inputData.employeeId;
    let payslipId=inputData.payslipId;
    let orgCode=inputData.orgCode;
    let payslipMonth=inputData.payslipMonth;
    let draftFileName=inputData.draftFileName;
    let templateId=inputData.templateId;
    let employeeName=inputData.employeeName;
    let payslipType=inputData.payslipType;
    let addedBy=inputData.addedBy;

    try{
        // get database connection
        let organizationDb = await makeConnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);
        if(organizationDb){

        // get employee timestamp based on location
        let employeeTimeZone = await empTimeZoneDetails(addedBy, organizationDb);
        
        // form password for encrypting the pdf
        let pdfEncryption = await encryptionCredentials(organizationDb,payslipId,requestId,employeeId,employeeTimeZone);
        
        // If success 4 parameters are returned in response
        if(Object.keys(pdfEncryption).length===4)
        {    
            // example 04,2019 - convert as 042019
            let definedPayslipMonth= payslipMonth.replace(/[,\s]/g, '');
            // example Payslip_10_042019.pdf
            let payslipFilename=process.env.payslipFileNamePrefix+pdfEncryption.userDefinedId+'_'+definedPayslipMonth+'.pdf';
            let employeeMail=pdfEncryption.employeeMail;
            // protect pdf with 2 password
            let userPassword= pdfEncryption.userPassword;
            let ownerPassword= pdfEncryption.ownerPassword;
            // destroy the database connection
            organizationDb.destroy();
            const response = 
            {
                string: 'NextStep',
                input:{'requestId':requestId,'payslipId':payslipId,'orgCode':orgCode,'addedBy':addedBy,'emailId':employeeMail,'payslipFileName':payslipFilename,'templateId':templateId,'employeeName':employeeName,'payslipMonth':payslipMonth,'payslipType':payslipType,'userPassword':userPassword,'ownerPassword':ownerPassword,'draftFileName':draftFileName,'employeeId':employeeId},
                message: 'Pdf generation initiated successfully'
            };
            return response;
        }
        else{
            // destroy the database connection
            organizationDb.destroy();
            const response = 
            {
                string: 'ErrorState',
                input:{'errorCode':pdfEncryption,'payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},
                message: 'Error occured in pdf generation function'
            };
            return response;
        }
    }
    else{
        console.log('Error in making database connection');
        const response = 
        {
          string: 'ErrorState',
          input:{'errorCode':'_DB0000','payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},
          message: 'Error occured in pdf generation function'
        };
        return response;      
    }
    }
    catch(error){
        // destroy the database connection
        organizationDb.destroy();
        
        console.log('Err in pdf generation initiation function',error);
        const response = 
        {
            string: 'ErrorState',
            input:{'errorCode':'PBP0102','payslipId':payslipId,'orgCode':orgCode,'requestId':requestId,'addedBy':addedBy,'templateId':templateId,'employeeName':employeeName},
            message: 'Error occured in pdf generation'
        };
        return response;
    }
}


// Function to get the encryption credentials
async function encryptionCredentials(organizationDb,payslipId,requestId,employeeId,employeeTimeZone){
    try{
        return (
            organizationDb
            .transaction(function(trx){
                return (
                    // update the Generation_Status as generating for the payslip
                    organizationDb('payslip_bulk_processing')
                    .update({ 'Generation_Status': 'Generating','Updated_On':employeeTimeZone })
                    .where('Payslip_Id',payslipId)
                    .andWhere('Request_Id',requestId)
                    .transacting(trx)
                    .then((updateStatus) =>
                    {
                        return(
                            // Get the Dob and name from personal table and empId,DOB from job table
                            organizationDb('emp_personal_info')
                            .select('emp_job.User_Defined_EmpId', 'emp_job.Emp_Email','emp_personal_info.Emp_First_Name','emp_personal_info.DOB')
                            .innerJoin('emp_job', 'emp_personal_info.Employee_Id', 'emp_job.Employee_Id')
                            .where('emp_job.Employee_Id', employeeId)
                            .transacting(trx)
                            .then((getpersonalInfo) =>
                            {
                                // Check whether details exists or not
                                if(getpersonalInfo.length>0)
                                {
                                    let empFirstName=getpersonalInfo[0].Emp_First_Name;
                                    // get first 4 character of emp_name and trim all the spaces
                                    let definedEmpName= empFirstName.replace(/ /g,'').substring(0,4);
                                    let empDOB= getpersonalInfo[0].DOB;
                                    // get DOB of employee in date format
                                    let splitDob=moment(empDOB).format('DD-MM-YYYY');
    
                                    let definedDOB= splitDob.replace(/[-\s]/g, '');
                        
                                    let userDefinedId= getpersonalInfo[0].User_Defined_EmpId;
                                    let employeeMail =getpersonalInfo[0].Emp_Email;
                                    // Get the orgcode and start year
                                    return(
                                        organizationDb('org_details')
                                        .select('Org_Code','Start_Year')
                                        .transacting(trx)
                                        .then(async(getOrgDetails) =>
                                        {
                                            // Check org details exist or not
                                            if(getOrgDetails.length>0){
                                                // get the organization start year
                                                let startYear= (getOrgDetails[0].Start_Year).split(',');
                                                let ownerPassword=getOrgDetails[0].Org_Code+startYear[1].trim();
                                                let userPassword=definedEmpName.toLowerCase()+definedDOB;
                                                return ({'userDefinedId':userDefinedId,'employeeMail':employeeMail,'userPassword':userPassword,'ownerPassword':ownerPassword});
                                            }   
                                            else{
                                                console.log('No org details exists');
                                                throw '_EC0001';  
                                            }
                                        }) 
                                    )
                                }
                                else{
                                    console.log('no personal and job details exists');
                                    throw '_EC0001';  
                                }
                            })
                        )
                    }
                    )
                )
            })
            /**return the success result to user */
            .then(function (result) {
                return result;
            })
            .catch(function(err) {
                console.log('Err in fetching pdf encryption credentials',err);
                // Check the error code
                let errCode=(err==='_EC0001') ? '_EC0001': 'PBP0112'
                return errCode;
            })
        );
    }
    catch(error){
        console.log('Error in retrieve encryption credentials catch block',error);
        return 'PBP0112';
    }
};