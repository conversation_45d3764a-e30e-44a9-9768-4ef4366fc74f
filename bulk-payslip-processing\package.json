{"name": "bulkpayslipprocessing", "version": "1.0.0", "description": "bulk payslip processing", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index", "lint": "eslint . --cache", "lint:updated": "pipe-git-updated --ext=js -- eslint --cache", "prettier-check": "prettier -c --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettify:updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"axios": "^0.19.2", "child_process": "^1.0.2", "fs": "0.0.2", "handlebars": "^4.7.6", "jquery": "^3.5.0", "jsdom": "^16.2.2", "knex": "^0.20.15", "moment": "^2.29.1", "moment-timezone": "^0.5.28", "mysql": "^2.18.1", "nodemailer": "^6.4.6", "path": "^0.12.7", "serverless-bundle": "github:suryacaprice/serverless-bundle#master", "serverless-plugin-split-stacks": "^1.9.3", "serverless-prune-plugin": "^1.4.3", "serverless-pseudo-parameters": "^2.5.0", "serverless-step-functions": "^2.26.0", "serverless-offline": "^6.5.0"}, "engines": {"node": "16.x"}, "browser": {"child_process": false}}