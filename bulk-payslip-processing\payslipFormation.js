'use strict'
// require axios package
import axios from 'axios';
// require the file to get the employee time zone
import empTimeZoneDetails from './employeetimezone';
// require file to bind payslipdata in template
import bindData from './bindPayslipData';
// require file to make database connection
import makeConnection from './getConnection';
// require file to upload/retrieve data to S3
import crudS3Function from './s3CommonFunction';


// Function to get payslip data and bind the values in template
export const formPayslipData = async (event, context) => {
    try{

    let bucketName= process.env.bucketName;
    let inputData=event.input;
    let orgCode=inputData.orgCode;
    let payslipId=inputData.payslipId;
    let payslipType=inputData.payslipType;
    let templateId=inputData.templateId;
    let requestId=inputData.requestId;
    let addedBy=inputData.addedBy;
    let employeeName=inputData.employeeName;
    let payslipMonth=inputData.salaryMonth;
    let employeeId=inputData.employeeId;

    try{
        let PathForTemplate=payslipType;
        if(PathForTemplate.toLowerCase()==='bimonthly'){
            PathForTemplate='Monthly';
        }
        let payslipTemplate= process.env.domainName +'/'+ orgCode +'/'+ process.env.templatePrefix+'/'+PathForTemplate+'/'+templateId;

        // get payslip year
        let payslipYear=payslipMonth.split(',')[1];

        // get database connection
        let organizationDb = await makeConnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);
        if(organizationDb){
        
        // get timestamp based on employee region
        let employeeTimeZone = await empTimeZoneDetails(addedBy, organizationDb);

        // Retrieve payslipId,payslipType from payslip_bulk_processing table
        return(
            organizationDb
            .transaction(function(trx){
                return(
                    // Update the  generation status as 'Inprogress' in payslip_bulk_processing table
                    organizationDb('payslip_bulk_processing')
                    .update({ 'Generation_Status': 'Inprogress','Updated_On':employeeTimeZone })
                    .where('Payslip_Id',payslipId)
                    .andWhere('Request_Id',requestId)
                    .transacting(trx)
                    .then(async(updateStatus) =>
                    {
                        return(
                            // Get the report creator flag and display address in org details table
                            organizationDb('org_details')
                            .select('Show_Report_Creator','Display_Payslip_Address')
                            .transacting(trx)
                            .then(async(getOrgData) =>{
                                let reportCreator=getOrgData[0].Show_Report_Creator;
                                let displayAddress=getOrgData[0].Display_Payslip_Address;
                                // Get the payslip template from s3 bucket
                                let fileData= await crudS3Function.getS3file(payslipTemplate,bucketName);
                                let content=fileData.Body;
                                // Convert buffer to string
                                let templateContent=content.toString('utf8');
                                // Function to get the payslip data based on payslip Id
                                let formation=await getPayslipData(templateContent,payslipType,orgCode,payslipId,employeeName,reportCreator,displayAddress);
                                if(!formation)
                                {
                                    const response = 
                                    {
                                        string: 'ErrorState',
                                        input:{'errorCode':'PBP0107','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},
                                        message: 'Error occured in payslip formation function'
                                    };
                                    return response;     
                                }
                                else
                                {
                                    // create a draft file in s3 to store the html content
                                    let draftFileName='Draft_'+orgCode+requestId+payslipId+'Html.txt';
                                    let path= process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+payslipYear+'/'+payslipType+'/'+draftFileName;
                                    let fileData= await crudS3Function.uploadS3file(formation,path,bucketName,'application/txt');
                                    if(fileData){
                                        const response = 
                                        {
                                            string: 'CreatePdf',
                                            input:{'payslipId':payslipId,'requestId':requestId,'employeeId':employeeId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName,'payslipMonth':payslipMonth,'payslipType':payslipType,'draftFileNamePath':path},
                                            message: 'Payslip formation completed successfullly'
                                        };
                                        return response;
                                    }
                                    else{
                                        console.log('Error in uploading template content in s3')
                                        const response = 
                                        {
                                            string: 'ErrorState',
                                            input:{'errorCode':'PBP0104','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},
                                            message: 'Error occured in payslip formation function'
                                        };
                                        return response;
                                    }
                                }
                            })
                        )
                    })
                )
            })
            /**return the success result to user */
            .then(function (result) {
                return result;
            })
            /**catch organizationDbConnection connectivity errors */
            .catch(function (err) {
                console.log('error in payslip formation catch block', err);
                const response = 
                {
                    string: 'ErrorState',
                    input:{'errorCode':'PBP0109','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},
                    message: 'Error occured in payslip formation function'
                };
                return response;
            })
            .finally(() => {
                organizationDb.destroy();
            })
        )
        }
        else{
            console.log('Error in making database connection');
            const response = 
            {
              string: 'ErrorState',
              input:{'errorCode':'_DB0000','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},
              message: 'Error occured in payslip formation function'
            };
            return response;          
        }
    }
    catch(err){
        console.log('Error in payslip formation common catch block',err);
        organizationDb.destroy();
        const response = 
        {
            string: 'ErrorState',
            input:{'errorCode':'PBP0109','payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName},
            message: 'Error occured in payslip formation function'
        };
        return response;
    }
}
catch(err)
{
    console.log("Error in main catch block",err);
    return err;
}
}

// form payslipData and bind with payslip template
async function getPayslipData(fileData,payslipType,orgCode,payslipId,employeeName,reportCreator,displayAddress){
    return new Promise(async function (resolve, reject) {
    try{
        // formation of url to get payslipdata based on type
        let formPayslipData=(payslipType==='Monthly')?process.env.getMonthlyPayslipdata: payslipType==='Hourly'?process.env.getHourlyPayslipdata:process.env.getBiMonthlyPayslipdata;
        let header=(process.env.domainName==='upshotindia')?'http:':'https:'
        let url= header+'//'+orgCode+'.'+process.env.domainName+process.env.webAddress+'/'+formPayslipData+'/'+payslipId+'/calledFrom/payout';
        // make api call to get payslip data
        await axios.get(url)
        .then(async function (response) {
            // check whether data exist or not
            if(response.data){
                let result= response.data;
                // bind the payslipdata along with template
                let bindPayslipValue = await bindData(fileData,result,payslipType,employeeName,reportCreator,displayAddress);
                console.log('Response from bindPayslipValue function',bindPayslipValue);   
                if(!bindPayslipValue){
                    console.log('error in binding value function');
                    resolve('');
                }
                else{
                    resolve(bindPayslipValue);   
                }
            }
            else{
                resolve('');
            }
        })
        .catch(function (error) {
           console.log('Error in retrieve payslip data',error);
           resolve('');
        });
    }
    catch(err){
        console.log('Error in getPayslipData function catch block',err);
        return '';
    }
    });
}
