'use strict'
// require aws-sdk to use aws services
import AWS from 'aws-sdk';
// Create object for s3 bucket
const S3 = new AWS.S3();

const funcs = {
    // Function to upload file in s3 bucket
    uploadS3file: (data,path,bucket,contenttype) => {
        return new Promise(function (resolve, reject) {
            const params = {
                Bucket: bucket,
                Key: path,
                Body: data,
                ContentType: contenttype
            };
            S3.putObject(params, function(s3Err, s3Data) {
                if (s3Err)
                {
                    console.log('Error in uploading file in s3',s3Err);
                    resolve('');
                }
                else{
                    console.log('File uploaded in bucket',s3Data);
                    resolve(s3Data);
                }
            });
        })
    },
    // Delete file from s3 bucket
    deleteFile: (path,bucket) => {
        return new Promise(function (resolve, reject) {
            const params = {
            Bucket: bucket,
            Key: path,
        };
        S3.deleteObject(params, function (err, data) {
            if (data) {
                resolve('');
            }
            else {
                console.log('Error in deleting file from s3 ',err);
                resolve('');
            }
        });
    });
    },
    // S3 file retrieve function
    getS3file: (key,bucket) => {
        return new Promise(function (resolve, reject) {
            S3.getObject(
                {
                    Bucket: bucket,
                    Key: key
                },
                function (err, data) {
                    if (err) 
                    {
                        console.log('Error in getting file from s3',err);
                        reject('PBP0110');
                    }
                    else {
                        resolve (data);
                    }
                }
            );
        })
    }

};

export default funcs;