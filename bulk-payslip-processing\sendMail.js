'use strict'
// require aws-sdk to use aws services
import AWS from 'aws-sdk';
// require ses for sending mail
let ses = new AWS.SES({
    region: process.env.sesTemplatesRegion });
// require nodemailer for sending mail with attachment
import nodemailer from 'nodemailer';
// require the file to get the employee time zone
import empTimeZoneDetails from './employeetimezone';
// require file to make database connection
import makeConnection from './getConnection';
// require file to get data from S3
import crudS3Function from './s3CommonFunction';
// require handlebars to bind value in template
import handlebars from 'handlebars';
// require fs package
import fs from 'fs';
// require path 
import path from 'path';
// Create object for s3 bucket
const S3 = new AWS.S3();

// Function to email the payslip as a attachment
export const emailCommunication = async (event, context) => {

    let inputData= event.input;
    let requestId=inputData.requestId;
    let payslipId=inputData.payslipId;
    let orgCode=inputData.orgCode;
    let fileName=inputData.payslipFileName;
    let emailId = inputData.emailId;
    let templateId=inputData.templateId;
    let employeeName=inputData.employeeName;
    let payslipMonth=inputData.payslipMonth;
    let payslipType=inputData.payslipType;
    let Encrypted=inputData.Encrypted;
    let addedBy=inputData.addedBy;

    // For calculating the salary month(In mail subject)
    let months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
        ];
          
    try{
        let organizationName;

        // get database connection
        let organizationDb = await makeConnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);
        if(organizationDb){
            try{
            // get timezone based on employee location
            let employeeTimeZone = await empTimeZoneDetails(addedBy, organizationDb);
        
            // Update the status in payslip_bulk_processing table
            let databaseUpdation = await statusUpdate(organizationDb,payslipId,requestId,employeeTimeZone,'Inprogress');
            
            organizationName= await getOrganizationDetails(organizationDb,orgCode);
            // In case of error only one param is returned so it will be handled in else part
            if(Object.keys(organizationName).length>1){
                // Form subject for mail as orgname+payslip+month(in words)
                let salaryMonth=payslipMonth.split(',')[0];
                let salaryYear=payslipMonth.split(',')[1];
                // Get the month in words
                let month_index = parseInt(salaryMonth) - 1;

                let payslipMonthInWords=months[month_index];
                // formation of mail subject
                let mailSubject= organizationName.orgname+' ' + process.env.mailSubject+' '+ payslipMonthInWords +' '+salaryYear;
                
                // formation of s3 url path
                let path= process.env.domainName+'/'+orgCode+'/'+process.env.s3PayslipPath+'/'+salaryYear+'/'+payslipType+'/'+fileName;

                // function to get the file from s3
                let getData=await crudS3Function.getS3file(path,process.env.bucketName);
                if(getData){
                    // get the mail template and bind the values in template 
                    let mailData=await getEmailTemplate(organizationName,mailSubject,payslipMonthInWords,salaryYear,emailId,getData,fileName,employeeName,Encrypted);
                    if(mailData==='success'){
                        // update the Email_Status as 'completed'
                        let databaseUpdation = await statusUpdate(organizationDb,payslipId,requestId,employeeTimeZone,'Completed');
                        const response = 
                        {
                            string: 'Step1',
                            input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},
                            message: 'Email send successfully'
                        };
                        // destroy database connection
                        organizationDb.destroy();
                        return response;
                    }
                    else{
                        organizationDb.destroy();
                        // return error response
                        let response= await outputResponse(mailData,payslipId,requestId,orgCode,templateId,employeeName,addedBy);
                        return response;
                    }
                }            
                else   
                {         
                    // err in retrieve file from s3
                    console.log('Error getting attachment from S3');
                    organizationDb.destroy();
                    // return error response
                    let response= await outputResponse('PBP0110',payslipId,requestId,orgCode,templateId,employeeName,addedBy);
                    return response;
                }
            }
            else{
                console.log('Error in retrieve organization details');
                organizationDb.destroy();
                // return error response
                let response= await outputResponse(organizationName.errCode,payslipId,requestId,orgCode,templateId,employeeName,addedBy);
                return response;
            }
        }
        catch(err){
            console.log('Error in send mail function catch block after database connection',err);
            organizationDb.destroy();
            // return error response
            let response= await outputResponse('PBP0114',payslipId,requestId,orgCode,templateId,employeeName,addedBy);
            return response;
        }
        }
        else{
            console.log('Error in making database connection');
            // return error response
            let response= await outputResponse('_DB0000',payslipId,requestId,orgCode,templateId,employeeName,addedBy);
            return response;    
        }
    }
    catch(error){
        console.log('error in mail commmunication common catch block',error);
        let errCode=(error==='PBP0110')?'PBP0110':'PBP0114';
        // return error response
        let response= await outputResponse(errCode,payslipId,requestId,orgCode,templateId,employeeName,addedBy);
        return response;
    }
};

// return output error response
function outputResponse(errCode,payslipId,requestId,orgCode,templateId,employeeName,addedBy){
    const response = 
    {
        string: 'ErrorState',
        input:{'errorCode':errCode,'payslipId':payslipId,'requestId':requestId,'addedBy':addedBy,'orgCode':orgCode,'templateId':templateId,'employeeName':employeeName,'source':'mail'},
        message: 'Error in mail communication'
    };
    return response;
}

// Function to send mail
function sendMail(transporter,mailOptions)
{
    return new Promise(function (resolve, reject) {
        // send email
        transporter.sendMail(mailOptions, function (err, info) {
            if (err) {
                console.log('Error sending email',err);
                resolve('PBP0105');
            } else {
                resolve('success');
            }
        });
    });
}

// update the emailStatus in payslip_bulk_processing table
async function statusUpdate(organizationDb,payslipId,requestId,employeeTimeZone,status){
    return (
        organizationDb('payslip_bulk_processing')
        .update({ 'Email_Status': status,'Updated_On':employeeTimeZone })
        .where('Payslip_Id',payslipId)
        .andWhere('Request_Id',requestId)
        .then((updateStatus) =>
        {
            return '';
        })
        .catch(function(err) {
            console.log('Error in statusUpdate function',err);
            throw 'PBP0001';
        })
    )
}

// Get organization details in database for sending email template
async function getOrganizationDetails(organizationDb,orgCode){
    try{
        return (
            // get the org_name and address from org details and location table
            organizationDb('location')
            .select('location.Street1', 'location.Street2', 'location.Pincode', 'state.State_Name', 'country.Country_Name', 'city.City_Name','org_details.Org_Name','org_details.HR_Admin_Email_Address','org_details.Report_LogoPath')
            .from('location')
            .leftJoin('country', 'location.Country_Code','country.Country_Code')
            .leftJoin('state', 'location.State_Id','state.State_Id')
            .leftJoin('city', 'location.City_Id', 'city.City_Id')
            .innerJoin('org_details')
            .where('location.Location_Type','MainBranch')
            .then(async(getorgdetails) =>
            {
                // Check whether data exist or not
                if(getorgdetails.length>0){
                    let url;

                    let OrgName=getorgdetails[0].Org_Name;
                    let street1=getorgdetails[0].Street1;
                    let street2=getorgdetails[0].Street2;
                    let city=getorgdetails[0].City_Name;
                    let country=getorgdetails[0].Country_Name;
                    let state=getorgdetails[0].State_Name;
                    let pincode=getorgdetails[0].Pincode;
                    let hrEmail=getorgdetails[0].HR_Admin_Email_Address;
                    let orgLogo=getorgdetails[0].Report_LogoPath;
                    // If logo exist get the presigned url
                    if(orgLogo)
                    {
                        let filePath = process.env.domainName + '_upload/' + orgCode+ '_tmp/logos/' + orgLogo;
                        let bucket = process.env.logoBucket; // HRAPP report logo bucket

                        // Call function headObject to check files exists or not in s3 bucket. Pass bucket name and file name as input
                        await S3.headObject({ Bucket: bucket, Key: filePath }).promise();

                        // Call function getSignedUrl and pass getObject/putObject function to getpresigned url
                        let reportLogoS3Path = S3.getSignedUrl('getObject', { Bucket: bucket, Key: filePath});
                        url= reportLogoS3Path.split('?')[0];
                    }
                    return ({'orgname':OrgName,'street1':street1,'street2':street2,'city':city,'country':country,'state':state,'pincode':pincode,'hrEmail':hrEmail,'orgLogo':url})
                }
                else{
                    console.log('No organization exists');
                    return {'errCode':'_EC0001'};
                }
            })
            .catch(function(err) {
                console.log('Err in getting organization details',err);
                return {'errCode':'PBP0002'};
            })
        )
    }
    catch(error){
        console.log('error in getOrganizationDetails catch block',error);
        return {'errCode':'PBP0002'};
    }
}

// get the email template and bind the content. Send mail along with template
async function getEmailTemplate(organizationName,mailSubject,payslipMonthInWords,salaryYear,emailId,getData,fileName,employeeName,Encrypted){
    return new Promise(function (resolve, reject) {
    try{
        // if file is encrypted need to send the template along with password hint 
        let htmlFile=(Encrypted)?'templateWithPassword.html':'templateWithoutPassword.html';
        // get the payslip template content from this file
        fs.readFile(path.join(__dirname, htmlFile), async function (err, templatedata) {
            if(err){
                console.log('Error in reading mail template content',err);
                resolve( 'PBP0113');
            }
            else{ 
                // complile the template with data
                let template = handlebars.compile(templatedata.toString('utf8'));
                // pass this params to html template and bind this values
                let replacements = {
                    hrEmail:organizationName.hrEmail?organizationName.hrEmail:'',
                    orgLogo:organizationName.orgLogo,
                    month:payslipMonthInWords,
                    year:salaryYear,
                    companyName:organizationName.orgname,
                    employeeName:employeeName,
                    street1:organizationName.street1,
                    street2:organizationName.street2,
                    city:organizationName.city,
                    pinCode:organizationName.pincode,
                    state:organizationName.state,
                    country:organizationName.country
                };
                // replace the values in template with these params
                let htmlToSend = template(replacements);
                
                let mailOptions = {
                    from: process.env.fromAddress,
                    subject: mailSubject,
                    html: htmlToSend,
                    to: emailId,
                    attachments: [
                        {
                            filename:fileName,
                            content: getData.Body
                        }
                    ]
                };
        
                // create Nodemailer SES transporter
                let transporter = nodemailer.createTransport({
                    SES: ses
                });
                // function to mail the content
                let sendEmailData= await sendMail(transporter,mailOptions);
                if(sendEmailData==='success'){
                    resolve ('success');
                }
                else{
                    resolve('PBP0105');
                }
            }
        })
    }
    catch(err){
        console.log('error in getEmailTemplate function',err);
        resolve('PBP0105');
    }
    })
}
