service: BULKPDFGENERATION # service name

plugins:
    - serverless-bundle
    - serverless-offline # require plugins 
    - serverless-pseudo-parameters
    - serverless-step-functions
    - serverless-plugin-split-stacks
    - serverless-prune-plugin # PlugIn to maintain lambda versoning

provider:
    name: aws
    runtime: nodejs16.x #nodejs run time
    stage: ${opt:stage} # get current stage name
    region: ${opt:region} #region in which to be deployed
    role: ${file(config.${self:provider.stage}.json):lambdaRole} # Assign role to the lambda functions
    vpc:
        securityGroupIds: ${file(config.${self:provider.stage}.json):securityGroupIds}
        subnetIds: ${file(config.${self:provider.stage}.json):subnetIds}
custom:  
    splitStacks:
      perFunction: true
      perType: true
    prune:
        automatic: true
        number: 3
    bundle:
      forceInclude: # Optional list of NPM packages that need to be included
        - mysql
      copyFiles:                # Copy any additional files to the generated package 
        - from: './bin/*'        # Where the files are currently 
          to: './'                # Where in the package should they go 
        - from: './sharedlib/*'        # Where the files are currently 
          to: './' 
        - from: './templateWithoutPassword.html'        # Where the files are currently 
          to: './templateWithoutPassword.html' 
        - from: './templateWithPassword.html'        # Where the files are currently 
          to: './templateWithPassword.html' 
      packager: npm # Specify a packager, 'npm' or 'yarn'. Defaults to 'npm'.
      caching: true # Enable Webpack caching
      linting: false # Enable linting as a part of the build process
      stats: false # Don't print out any Webpack output
      sourcemaps: true # Enable source maps

package:
  include:
    - bin/*
    - sharedlib/*
  individually: true
    
# Lambda functions 
functions:
    checkPayslipExistence:
        handler: checkPayslipExistence.startFunction
        timeout: 900
        memorySize: 3008
        environment: 
            dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
            region      : ${self:provider.region}
            dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
    payslipFormation:
        handler: payslipFormation.formPayslipData
        timeout: 900
        memorySize: 3008
        layers:
              - ${file(config.${self:provider.stage}.json):layers}
        environment: 
            bucketName: ${file(config.${self:provider.stage}.json):bucketName}
            dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
            region      : ${self:provider.region}   
            webAddress : ${file(config.${self:provider.stage}.json):webAddress}     
            templatePrefix: Payslip Template
            s3PayslipPath: Salary Payslip
            dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
            domainName  : ${file(config.${self:provider.stage}.json):domainName}
            getMonthlyPayslipdata: payroll/salary-payslip/view-salary-payslip/payslipId    
            getHourlyPayslipdata: payroll/salary-payslip/view-wage-payslip/payslipId
            getBiMonthlyPayslipdata: payroll/salary-payslip/view-bwd-salary-payslip/payslipId       
    payslipMailCommunication:
        handler: sendMail.emailCommunication
        timeout: 900
        memorySize: 3008
        environment: 
            bucketName: ${file(config.${self:provider.stage}.json):bucketName}
            dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
            region      : ${self:provider.region}
            sesTemplatesRegion : ${file(config.${self:provider.stage}.json):sesTemplatesRegion}
            fromAddress: ${file(config.${self:provider.stage}.json):fromAddress}
            mailSubject: Payslip for
            dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
            domainName  : ${file(config.${self:provider.stage}.json):domainName}
            s3PayslipPath: Salary Payslip
            logoBucket: ${file(config.${self:provider.stage}.json):logoBucket}
    updateStatus:
        handler: updateStatus.updateStatus
        timeout: 900
        memorySize: 3008
        environment: 
            dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}	            
            dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
            region      : ${self:provider.region}  
    initiatePdfGeneration:
        handler: initiatePdfGeneration.generateEncryptedPdf
        timeout: 900
        memorySize: 3008
        environment: 
            dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
            region      : ${self:provider.region}  
            dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
            bucketName: ${file(config.${self:provider.stage}.json):bucketName}
            payslipFileNamePrefix: Payslip_
            s3PayslipPath: Salary Payslip
            domainName  : ${file(config.${self:provider.stage}.json):domainName}
    pdfCreation:
        handler: createPdf.pdfCreation
        timeout: 900
        memorySize: 3008
        environment: 
            bucketName: ${file(config.${self:provider.stage}.json):bucketName}
            region      : ${self:provider.region}  
            domainName  : ${file(config.${self:provider.stage}.json):domainName}
            s3PayslipPath: Salary Payslip
            dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
            dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
            payslipFileNamePrefix: Payslip_
            stageName: ${self:provider.stage}
    pdfEncryption:
        handler: encryptPdf.encrypting
        timeout: 900
        memorySize: 3008
        environment: 
            dbSecretName: ${file(config.${self:provider.stage}.json):dbSecretName}
            region      : ${self:provider.region}  
            dbPrefix: ${file(config.${self:provider.stage}.json):dbPrefix}
            bucketName: ${file(config.${self:provider.stage}.json):bucketName}
            payslipFileNamePrefix: Payslip_
            s3PayslipPath: Salary Payslip
            domainName  : ${file(config.${self:provider.stage}.json):domainName}
stepFunctions:
  stateMachines:
    bulkPayslipProcessing:
      name: ${opt:stage}-bulkPayslipProcessing
      events:
        - http:
            path: bulkPayslipStepFunction
            method: POST
            cors: true
            authorizer:
              arn: ${file(config.${self:provider.stage}.json):authorizerARN}
              resultTtlInSeconds: 0
              type: request
      definition:
        Comment: 'User can communicate bulk payslip through email using this function'
        StartAt: CheckPayslipExistence
        States:
          CheckPayslipExistence:
            Type: Task
            Resource: arn:aws:lambda:#{AWS::Region}:#{AWS::AccountId}:function:${self:service}-${opt:stage}-checkPayslipExistence
            Next: ChoiceState
          ChoiceState:
            Type: Choice
            Choices:
            - Variable: '$.string'
              StringEquals: Mail
              Next: SendMail
            - Variable: '$.string'
              StringEquals: NextStep
              Next: FormPayslipData
            - Variable: '$.string'
              StringEquals: Success
              Next: EndFunction
            - Variable: '$.string'
              StringEquals: Step1
              Next: CheckPayslipExistence
            Default: ErrorState
          SendMail:
            Type: Task
            Resource: arn:aws:lambda:#{AWS::Region}:#{AWS::AccountId}:function:${self:service}-${opt:stage}-payslipMailCommunication
            Next: MailChoiceState
          MailChoiceState:
            Type: Choice
            Choices:
            - Variable: '$.string'
              StringEquals: Step1
              Next: CheckPayslipExistence
            Default: ErrorState
          FormPayslipData:
            Type: Task
            Resource: arn:aws:lambda:#{AWS::Region}:#{AWS::AccountId}:function:${self:service}-${opt:stage}-payslipFormation
            Next: PayslipFormationChoiceState
          PayslipFormationChoiceState:
            Type: Choice
            Choices:
            - Variable: '$.string'
              StringEquals: CreatePdf
              Next: CreatePdf
            Default: ErrorState
          InitiatePdfGeneration:
            Type: Task
            Resource: arn:aws:lambda:#{AWS::Region}:#{AWS::AccountId}:function:${self:service}-${opt:stage}-initiatePdfGeneration
            Next: InitiatePdfGenerationChoiceState
          InitiatePdfGenerationChoiceState:
            Type: Choice
            Choices:
            - Variable: '$.string'
              StringEquals: NextStep
              Next: EncryptPdf
            Default: ErrorState
          ErrorState:
            Type: Task
            Resource: arn:aws:lambda:#{AWS::Region}:#{AWS::AccountId}:function:${self:service}-${opt:stage}-updateStatus
            Next: ErrorChoiceState
          ErrorChoiceState:
            Type: Choice
            Choices:
            - Variable: '$.string'
              StringEquals: Step1
              Next: CheckPayslipExistence
            Default: EndFunction
          EndFunction:
            Type: Pass
            Result: 'Step function execution completed'
            End: true
          CreatePdf:
            Type: Task
            Resource: arn:aws:lambda:#{AWS::Region}:#{AWS::AccountId}:function:${self:service}-${opt:stage}-pdfCreation
            Next: CreatePdfChoiceState
          CreatePdfChoiceState:
            Type: Choice
            Choices:
            - Variable: '$.string'
              StringEquals: GeneratePdf
              Next: InitiatePdfGeneration
            - Variable: '$.string'
              StringEquals: Step1
              Next: CheckPayslipExistence
            - Variable: '$.string'
              StringEquals: Mail
              Next: SendMail
            Default: ErrorState
          EncryptPdf:
            Type: Task
            Resource: arn:aws:lambda:#{AWS::Region}:#{AWS::AccountId}:function:${self:service}-${opt:stage}-pdfEncryption
            Next: EncryptPdfChoiceState
          EncryptPdfChoiceState:
            Type: Choice
            Choices:
            - Variable: '$.string'
              StringEquals: Mail
              Next: SendMail
            - Variable: '$.string'
              StringEquals: Step1
              Next: CheckPayslipExistence
            Default: ErrorState
