'use strict';
// require package
const chromium = require('@sparticuz/chrome-aws-lambda');

// function to upload actual image and thumbnail image in AWS S3 bucket
//Get the employee work schedule details
module.exports.createPDF = async (event, args, context, info) => {
    
    try{
        // require npm packages to upload files
        let {pathOfFileToEncrypt,inputData} = event;
        
        // pdf will be created based on the options
        const pdfOptions = {
            path: pathOfFileToEncrypt,
            format: 'A4',
            margin: {
                top: 0,
                right: 0,
                bottom: 0,
                left: 0
            },
            printBackground: true 
        };
        
        // create browser
        const browser = await chromium.puppeteer.launch({
            args: chromium.args,
            defaultViewport: chromium.defaultViewport,
            executablePath: await chromium.executablePath,
            headless: true,
        });


        const page = await browser.newPage();

        // set the content in a pdf file
        await page.setContent(inputData);

        let content=await page.pdf(pdfOptions);   
        return content;     
        
    } catch (createPdfErr){
        console.log('Error in createPDF() function catch block', createPdfErr);
        return '';
    }
};