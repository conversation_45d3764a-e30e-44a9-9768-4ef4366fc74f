// Function to update the job status as completed in bulk_processing table if all the records are processed
    export default async (organizationDb,status,employeeTimeZone,requestId,jobType) => {
        try{
            // if job type is email check generation and email status
            const checkBothStatus= organizationDb.raw('update bulk_processing set Job_Status="'+status+'",Updated_On= "'+employeeTimeZone+'" where Request_Id="'+requestId+'" and (select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id="'+requestId+'" and ((PBP.Email_Status="Completed" or PBP.Email_Status="Failure") and (PBP.Generation_Status="Completed" or PBP.Generation_Status="Failure" or PBP.Generation_Status="Already Generated")))=(select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id="'+requestId+'")');
            const checkGenerationStatus=organizationDb.raw('update bulk_processing set Job_Status="'+status+'",Updated_On= "'+employeeTimeZone+'" where Request_Id="'+requestId+'" and (select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id="'+requestId+'" and ((PBP.Generation_Status="Completed" or PBP.Generation_Status="Failure" or PBP.Generation_Status="Already Generated")))=(select COUNT(PBP.Request_Id) from payslip_bulk_processing as PBP where PBP.Request_Id="'+requestId+'")');
            // If job type is generation check generation_status else check both generation and email status
            const subQuery=(jobType==='Email')?checkBothStatus:checkGenerationStatus;
            return(
                subQuery                
                .then((updatejobStatus) =>
                {
                    if(updatejobStatus[0].affectedRows){
                        return 'completed';
                    }
                    else{
                        return '';
                    }
                })
                .catch(function(err) {
                    console.log('Err in updating job status',err);
                    return '';
                })
            );
        }
        catch(err){
            console.log('Error in updateJobstatus function',err);
            return '';
        }
    };
