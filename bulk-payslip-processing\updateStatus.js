'use strict'
// require the file to get the employee time zone
import empTimeZoneDetails from './employeetimezone';
// require the file to update the status
import updateJobStatus from './updateJobStatus';
// require file to make database connection
import makeConnection from './getConnection';


// Function to update the status in table
export const updateStatus = async (event, context) => {
    try{
        let inputData=event.input;
        let payslipId=inputData.payslipId;
        let requestId=inputData.requestId;
        let orgCode=inputData.orgCode;
        let errorCode=inputData.errorCode;
        let templateId=inputData.templateId;
        let addedBy=inputData.addedBy;

        // Check whether error returned from mail function
        let emailStatus=(inputData.source=='mail')?true:false;

        // If step 1 if the inputs are empty return the failure response.
        if(errorCode==='IVE0000')
        {
            const response = 
            {
                string: 'ErrorState',
                input:{'errorCode':'IVE0000'},
                message: 'Error in updateStatus function'
            };
            return response;
        }

        // get database connection
        let organizationDb = await makeConnection(process.env.dbSecretName,process.env.dbPrefix,orgCode,process.env.region);
        if(organizationDb){
            try{
                // get employee timestamp based on location
                let employeeTimeZone = await empTimeZoneDetails(addedBy, organizationDb);
                
                // if error occurs in mail function update the 'email status' otherwise update the generation status and email status
                if(emailStatus){
                    return(
                        // update the emial_status as 'Failure',error_code
                        organizationDb('payslip_bulk_processing')
                        .update({ 'Email_Status' : 'Failure', 'Error_Code':errorCode,'Updated_On':employeeTimeZone })
                        .where('Payslip_Id',payslipId)
                        .andWhere('Request_Id',requestId)
                        .then(async(updateStatus) =>
                        {
                            // function to return the output response
                            let returnOutput = await outputResponse(organizationDb,requestId,employeeTimeZone,templateId,orgCode,'Email');
                            if(returnOutput){
                                return returnOutput;
                            }
                            else{
                                const response = 
                                {
                                    string: 'ErrorState',
                                    input:{'errorCode':'PBP0001'},
                                    message: 'Error in updateStatus function'
                                };
                                return response;
                            }
                        })
                        .catch(function(err) {
                            console.log('Err in updating email status',err);
                            const response = 
                            {
                                string: 'ErrorState',
                                input:{'errorCode':'PBP0001'},
                                message: 'Error in updateStatus function'
                            };
                            return response;
                        })
                        .finally(() => {
                            organizationDb.destroy();
                        })
                    );
                }
                else
                {
                    // get the job type for this requestId
                    let getType = await getJobType(organizationDb,requestId)
                    if(getType){
                        // if job type is email update both generation and email status as 'failure'
                        let updateDetails=(getType=='Email')?{ 'Generation_Status' : 'Failure', 'Email_Status' : 'Failure','Error_Code':errorCode,'Updated_On':employeeTimeZone }:{ 'Generation_Status' : 'Failure', 'Error_Code':errorCode,'Updated_On':employeeTimeZone };
                        return(
                            // update the generation_status as 'Failure' and Error_code
                            organizationDb('payslip_bulk_processing')
                            .update(updateDetails)
                            .where('Payslip_Id',payslipId)
                            .andWhere('Request_Id',requestId)
                            .then(async(updateStatus) =>
                            {
                                // function to return the response
                                let returnOutput = await outputResponse(organizationDb,requestId,employeeTimeZone,templateId,orgCode,getType);
                                if(returnOutput){
                                    // destroy database connection
                                    organizationDb.destroy();
                                    return returnOutput;
                                }
                                else{
                                    // destroy database connection
                                    organizationDb.destroy();
                                    const response = 
                                    {
                                        string: 'ErrorState',
                                        input:{'errorCode':'PBP0001'},
                                        message: 'Error in updateStatus function'
                                    };
                                    return response;
                                }
                            })
                            .catch(function(err) {
                                console.log('Err in updating generation and email status',err);
                                const response = 
                                {
                                    string: 'ErrorState',
                                    input:{'errorCode':'PBP0001'},
                                    message: 'Error in updateStatus function'
                                };
                                // destroy database connection
                                organizationDb.destroy();
                                return response;
                            })
                        )
                    }
                    else{
                        console.log('error in retrieve job type');
                        const response = 
                        {
                            string: 'Step1',
                            input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},
                            message: 'Move to payslip generation step'
                        };
                        // destroy database connection
                        organizationDb.destroy();
                        return response; 
                    }
                }
            }
            catch(error){
                console.log('Error in common catch block after database connection',error);
                const response = 
                {
                    string: 'ErrorState',
                    input:{'errorCode':'_DB0000'},
                    message: 'Error in updateStatus function'
                };
                // destroy database connection
                organizationDb.destroy();
                return response;  
            }
        }
        else{
            console.log('Error in making database connection');
            const response = 
            {
                string: 'ErrorState',
                input:{'errorCode':'_DB0000'},
                message: 'Error in updateStatus function'
            };
            return response;      
        }
    }
    catch(error)
    {
        console.log('Error in update status function catch block',error);
        const response = 
        {
            string: 'ErrorState',
            input:{'errorCode':'PBP0001'},
            message: 'Error in updateStatus function'
        };
        return response;
    }
};

// function to get jobtype based on requestId
async function getJobType(organizationDb,requestId){
    return(
        organizationDb('bulk_processing')
        .select('Job_Type')
        .where('Request_Id',requestId)
        .then((getrecord) =>
        {
            if(getrecord.length>0){
                let jobType=getrecord[0].Job_Type;
                return jobType;
            }
            else
            {
                console.log('No jobtype exist for this request');
                return '';
            }

        })
        .catch(function(err) {
            console.log('Error in retrieve job type',err);
            return '';
        })
    )
}

// Function to return the output response based on condition
async function outputResponse(organizationDb,requestId,employeeTimeZone,templateId,orgCode,getType){
    try{
        // update the job status if all records are processed
        let status =await updateJobStatus(organizationDb,'Completed',employeeTimeZone,requestId,getType)
        if(status=='completed')
        {
            const response = 
            {
                string: 'Success',
                message: 'Step function completed'
            };
            return response;
        }
        else{
            const response = 
            {
                string: 'Step1',
                input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},
                message: 'Move to payslip generation step'
            };
            return response; 
        }
    }
    catch(error){
        console.log('error in outputResponse catch block',error);
        const response = 
        {
            string: 'Step1',
            input:{'requestId':requestId,'templateId':templateId,'orgCode':orgCode},
            message: 'Move to payslip generation step'
        };
        return response; 
    }
}