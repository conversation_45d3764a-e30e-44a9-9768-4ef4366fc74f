
'use strict'

export default async (organizationDb,fileName,requestId,payslipId,payslipType,employeeTimeZone,isEncrypted,employeeId) => {

    try{        
        // Based on type get the payslip table name
        const payslipTable = (payslipType==='Monthly') ? 'salary_payslip' : (payslipType==='Hourly') ? 'hourlywages_payslip': 'bwd_salary_payslip';
        
        // update the generation status as completed
        return(
            organizationDb('payslip_bulk_processing')
            .update({ 'Generation_Status': 'Completed','Updated_On':employeeTimeZone })
            .where('Payslip_Id',payslipId)
            .andWhere('Request_Id',requestId)
            .then((updateStatus) =>
            {
                return(
                    // update the filename and S3_File_Encrypted flag in payslip table
                    organizationDb(payslipTable)
                    .update({ 'S3_FileName': fileName, 'S3_File_Encrypted':isEncrypted })
                    .where('Payslip_Id',payslipId)
                    .andWhere('Employee_Id',employeeId)
                    .then((updateStatus) =>{
                        return 'success';
                    }
                    )
                )
            })
            .catch(function(err) {
                console.log('Err in updating status',err);
                return '';
            })
        );
    }
    catch(error){
        console.log('Error in updateFileName and status catch block',error);
        return '';
    }
    }